<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Label Editor (60×20 mm @203dpi)</title>
    <style>
      body {
        font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif;
        margin: 16px;
      }
      .row {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 8px;
      }
      label {
        min-width: 110px;
      }
      input,
      select,
      button {
        padding: 6px 8px;
      }
      #stageWrap {
        border: 1px solid #ccc;
        display: inline-block;
        background: #fff;
        overflow: hidden; /* Hide any overflow */
      }
      #stageWrap canvas {
        display: block !important;
      }
      #stage {
        background: #fff;
      }
      #status {
        margin-left: 8px;
        color: #333;
      }
    </style>
    <!-- Offline libraries (provide files in examples/lib) -->
    <script src="./lib/fabric.min.js"></script>
    <script src="./lib/qrcode.min.js"></script>
    <!-- QRCode loaded dynamically with fallback in JS -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;600;700&family=Noto+Sans+Thai:wght@400;600&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  </head>
  <body>
    <div class="max-w-7xl mx-auto p-4">
      <h1 class="text-xl font-semibold mb-4">
        Label Editor (60×20 mm @203dpi)
      </h1>
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- Left: controls (narrow) -->
        <div class="flex-1 flex flex-col gap-2">
          <div class="bg-white border rounded p-3">
            <h3 class="font-medium mb-2">Label Size</h3>
            <div class="flex flex-col gap-2">
              <label class="text-sm text-slate-600">Paper Size</label>
              <select id="paperSize" class="border rounded px-2 py-1">
                <option value="60x20" selected>60×20 mm</option>
                <option value="50x20">50×20 mm</option>
                <option value="50x30">50×30 mm</option>
                <option value="40x30">40×30 mm</option>
                <option value="75x130">75×130 mm</option>
                <option value="custom">Custom Size</option>
              </select>
              <div id="customSizeInputs" class="hidden flex gap-2 items-center">
                <div class="flex items-center gap-1">
                  <label class="text-xs text-slate-600">W:</label>
                  <input id="customWidth" type="number" value="60" min="10" max="200" step="1" class="border rounded px-2 py-1 w-16 text-sm" />
                  <span class="text-xs text-slate-500">mm</span>
                </div>
                <div class="flex items-center gap-1">
                  <label class="text-xs text-slate-600">H:</label>
                  <input id="customHeight" type="number" value="20" min="10" max="200" step="1" class="border rounded px-2 py-1 w-16 text-sm" />
                  <span class="text-xs text-slate-500">mm</span>
                </div>
                <button id="applyCustomSize" class="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">Apply</button>
              </div>
              <div class="mt-3">
                <label class="text-sm text-slate-600 block mb-2">Orientation</label>
                <div class="inline-flex border rounded overflow-hidden" role="group">
                  <button type="button" id="orientationPortrait" class="px-3 py-2 hover:bg-slate-100 border-r bg-blue-500 text-white" title="Portrait">
                    <svg width="20" height="24" viewBox="0 0 20 24" class="w-5 h-6">
                      <rect x="2" y="2" width="16" height="20" fill="none" stroke="currentColor" stroke-width="2"/>
                      <text x="10" y="14" text-anchor="middle" font-size="8" fill="currentColor">A</text>
                    </svg>
                  </button>
                  <button type="button" id="orientationLandscape" class="px-3 py-2 hover:bg-slate-100" title="Landscape">
                    <svg width="24" height="20" viewBox="0 0 24 20" class="w-6 h-5">
                      <rect x="2" y="2" width="20" height="16" fill="none" stroke="currentColor" stroke-width="2"/>
                      <text x="12" y="12" text-anchor="middle" font-size="8" fill="currentColor">A</text>
                    </svg>
                  </button>
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  <span id="orientationInfo">Portrait: 60×20 mm (W×H)</span>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-white border rounded p-3">
            <h3 class="font-medium mb-2">Print Settings</h3>
            <div class="flex flex-col gap-2">
              <label class="text-sm text-slate-600">Printer</label>
              <input id="printer" class="hidden" />
              <div class="border rounded p-2 bg-slate-50">
                <div class="flex items-center justify-between mb-2">
                  <div class="text-sm text-slate-600">Select a printer</div>
                  <button
                    id="refreshPrinters"
                    class="text-xs px-2 py-1 bg-slate-200 rounded hover:bg-slate-300"
                  >
                    Refresh
                  </button>
                </div>
                <div
                  id="printerList"
                  class="flex flex-col gap-1 max-h-44 overflow-auto"
                ></div>
              </div>
              <label class="hidden text-sm text-slate-600">Direction</label>
              <select
                id="direction"
                class="hidden border rounded px-2 py-1 w-40"
              >
                <option value="0">0 (front)</option>
                <option value="1" selected>1 (flip 180°)</option>
              </select>
              <div class="mt-2 flex items-center gap-3 flex-wrap">
                <div class="inline-flex items-center gap-2">
                  <label class="text-sm text-slate-600">Copies</label>
                  <input
                    id="copies"
                    type="number"
                    value="1"
                    min="1"
                    step="1"
                    class="border rounded px-2 py-1 w-20"
                  />
                </div>
                <div class="inline-flex items-center gap-2">
                  <label class="text-sm text-slate-600">Collation</label>
                  <select id="collation" class="border rounded px-2 py-1 w-36">
                    <option value="collated">Collated</option>
                    <option value="uncollated" selected>Uncollated</option>
                  </select>
                </div>
                <label
                  class="inline-flex items-center gap-2 text-sm text-slate-600"
                >
                  <input id="reverseOrder" type="checkbox" /> Reverse
                </label>
                <button
                  id="saveTpl"
                  class="px-3 py-2 bg-slate-200 rounded cursor-pointer hover:bg-slate-300 inline-flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="w-5 h-5"
                  >
                    <path
                      d="M17 3H5a2 2 0 00-2 2v12h2V7h12V3zm2 4h-4V3l4 4zM5 19h14v2H5v-2zm14-8H5a2 2 0 00-2 2v4h18v-4a2 2 0 00-2-2z"
                    />
                  </svg>
                  Save
                </button>
                <button
                  id="print"
                  class="px-5 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 text-base font-semibold shadow inline-flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="w-5 h-5"
                  >
                    <path
                      d="M6 9V2h12v7H6zm10-2V4H8v3h8zM6 14H4a2 2 0 01-2-2V9a2 2 0 012-2h16a2 2 0 012 2v3a2 2 0 01-2 2h-2v5H6v-5zm2 0v3h8v-3H8z"
                    />
                  </svg>
                  Print
                </button>
                <span id="status" class="text-sm text-slate-600 ml-1"></span>
              </div>
            </div>
          </div>
          <div class="bg-white border rounded p-3">
            <h3 class="font-medium mb-2">Saved Templates</h3>
            <div class="flex flex-wrap gap-2">
              <div
                id="templates"
                class="grid grid-cols-1 sm:grid-cols-2 gap-3"
              ></div>
            </div>
          </div>
        </div>
        <!-- Right: elements, canvas, data, bindings, templates (wide) -->
        <div class="flex-1 flex flex-col gap-4">
          <!-- Add Elements on top of label area -->
          <div class="bg-white border rounded p-3">
            <h3 class="font-medium mb-2">Add Elements</h3>
            <div class="flex flex-wrap gap-2">
              <button
                id="addText"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Text"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path d="M3 4h18v2H13v14h-2V6H3z" />
                </svg>
                Text
              </button>
              <button
                id="addBarcode"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Barcode"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    d="M3 4h2v16H3V4zm3 3h1v10H6V7zm2-3h1v16H8V4zm2 3h2v10h-2V7zm3-3h1v16h-1V4zm2 3h1v10h-1V7zm2-3h2v16h-2V4z"
                  />
                </svg>
                Barcode
              </button>
              <button
                id="addQR"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add QR"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    d="M3 3h8v8H3V3zm2 2v4h4V5H5zm6 6h2v2h-2v-2zm0-8h2v6h-2V3zm4 0h6v6h-6V3zm2 2v2h2V5h-2zM3 13h6v8H3v-8zm2 2v4h2v-4H5zm8-2h3v3h-3v-3zm3 0h3v2h-1v3h-2v-5zm-3 5h3v3h-3v-3zm5 0h3v3h-3v-3z"
                  />
                </svg>
                QR
              </button>
              <label
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 cursor-pointer inline-flex items-center gap-2"
                title="Upload Image"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    d="M5 3h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2zm0 2v10l3.5-3.5 2.5 2.5L15 9l4 6V5H5z"
                  />
                </svg>
                <input
                  id="imgFile"
                  type="file"
                  accept="image/*"
                  class="hidden"
                />
                Image
              </label>
              <button
                id="addLine"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Line"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <rect x="4" y="11" width="16" height="2" />
                </svg>
                Line
              </button>
              <button
                id="addRect"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Rectangle"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <rect x="5" y="5" width="14" height="10" />
                </svg>
                Rect
              </button>
              <button
                id="addCircle"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Circle"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <circle cx="12" cy="12" r="5" />
                </svg>
                Circle
              </button>
              <button
                id="addTriangle"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Triangle"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path d="M12 5l9 14H3l9-14z" />
                </svg>
                Triangle
              </button>
              <button
                id="addDate"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Current Date"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                Date
              </button>
              <button
                id="addTime"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Current Time"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm4.2 14.2L11 13V7h1.5v5.2l4.5 2.7-.8 1.3z"/>
                </svg>
                Time
              </button>
              <button
                id="addCounter"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
                title="Add Sequential Counter"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path d="M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z"/>
                </svg>
                Counter
              </button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
              <button
                id="bringFront"
                disabled
                class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2"
                title="Bring to front"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                  <path d="M7 7h10v10H7V7zm-4 0h2v10H3V7zm16 0h2v10h-2V7zm-8-4h2v2h-2V3zm0 18h2v2h-2v-2z"/>
                </svg>
                To Front
              </button>
              <button
                id="sendBack"
                disabled
                class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2"
                title="Send to back"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                  <path d="M7 7h10v10H7V7zm-4 0h2v10H3V7zm16 0h2v10h-2V7zM11 3h2v2h-2V3zm0 16h2v2h-2v-2z"/>
                </svg>
                To Back
              </button>
              <button
                id="undoBtn"
                class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    d="M12 5V2L7 7l5 5V9c3.31 0 6 2.69 6 6 0 1.12-.31 2.16-.85 3.05l1.46 1.46C19.42 18.35 20 16.75 20 15c0-4.42-3.58-8-8-8z"
                  />
                </svg>
                Undo
              </button>
              <button
                id="copy"
                disabled
                class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    d="M16 1H4a2 2 0 00-2 2v12h2V3h12V1zm3 4H8a2 2 0 00-2 2v16h13a2 2 0 002-2V7a2 2 0 00-2-2zm0 18H8V7h11v16z"
                  />
                </svg>
                Copy
              </button>
              <button
                id="del"
                disabled
                class="px-3 py-2 rounded bg-slate-200 text-slate-400 inline-flex items-center gap-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path d="M6 7h12l-1 14H7L6 7zm3-3h6l1 2H8l1-2z" />
                </svg>
                Delete
              </button>
              <button
                id="clearCanvas"
                class="px-3 py-2 bg-blue-500 text-white rounded cursor-pointer hover:bg-blue-400 inline-flex items-center gap-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    d="M3 6h18v2H3V6zm2 4h14l-1.5 10h-11L5 10zm5-6h4l1 2H9l1-2z"
                  />
                </svg>
                Clear Label
              </button>
            </div>
          </div>
          <!-- Label area -->
          <div
            class="bg-white border rounded p-3 flex justify-center items-center"
          >
            <div id="stageWrap" class="inline-block border rounded bg-white">
              <canvas id="stage" width="480" height="160"></canvas>
            </div>
          </div>
          <!-- Text settings -->
          <div class="bg-white border rounded p-3" id="textControls" style="display: none">
            <h3 class="font-medium mb-2">Text Settings</h3>
            <!-- Hidden state for compatibility with existing logic -->
            <input id="fontFamily" type="hidden" value="Sarabun" />
            <input id="fontSize" type="hidden" value="24" />
            <input id="fontWeight" type="hidden" value="600" />
            <input id="textAlign" type="hidden" value="left" />

            <!-- Toolbar: font family (segmented), size, styles, alignment -->
            <div class="flex items-center gap-3 flex-wrap">
              <!-- Family -->
              <div class="inline-flex border rounded overflow-hidden" role="group" aria-label="Font family">
                <button type="button" class="px-2 py-1 text-sm hover:bg-slate-100 tt-font" data-font="Sarabun" id="ffSarabun">Sarabun</button>
                <button type="button" class="px-2 py-1 text-sm hover:bg-slate-100 tt-font" data-font="Noto Sans Thai" id="ffNoto">Noto</button>
                <button type="button" class="px-2 py-1 text-sm hover:bg-slate-100 tt-font" data-font="system" id="ffSystem">System</button>
              </div>

              <!-- Size stepper -->
              <div class="inline-flex items-center border rounded overflow-hidden" aria-label="Font size">
                <button type="button" id="fsDec" class="px-2 py-1 hover:bg-slate-100">A-</button>
                <div id="fsDisp" class="px-2 py-1 w-10 text-center text-sm">24</div>
                <button type="button" id="fsInc" class="px-2 py-1 hover:bg-slate-100">A+</button>
              </div>

              <!-- Styles -->
              <div class="inline-flex border rounded overflow-hidden" role="group" aria-label="Font styles">
                <button type="button" id="stBold" class="px-2 py-1 hover:bg-slate-100" title="Bold (B)"><strong>B</strong></button>
                <button type="button" id="stItalic" class="px-2 py-1 hover:bg-slate-100 italic" title="Italic (I)">I</button>
                <button type="button" id="stUnderline" class="px-2 py-1 hover:bg-slate-100 underline" title="Underline (U)">U</button>
                <button type="button" id="stStrike" class="px-2 py-1 hover:bg-slate-100 line-through" title="Strikethrough (S)">S</button>
              </div>

              <!-- Alignment -->
              <div class="inline-flex border rounded overflow-hidden" role="group" aria-label="Alignment">
                <button type="button" id="alLeft" class="px-2 py-1 hover:bg-slate-100" title="Align Left">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path d="M3 4h18v2H3V4zm0 4h12v2H3V8zm0 4h18v2H3v-2zm0 4h12v2H3v-2z"/></svg>
                </button>
                <button type="button" id="alCenter" class="px-2 py-1 hover:bg-slate-100" title="Align Center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path d="M3 4h18v2H3V4zm3 4h12v2H6V8zm-3 4h18v2H3v-2zm3 4h12v2H6v-2z"/></svg>
                </button>
                <button type="button" id="alRight" class="px-2 py-1 hover:bg-slate-100" title="Align Right">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path d="M3 4h18v2H3V4zm6 4h12v2H9V8zM3 12h18v2H3v-2zm6 4h12v2H9v-2z"/></svg>
                </button>
              </div>

            </div>

            <div class="text-xs text-slate-500 mt-2">Text settings are applied automatically when changed.</div>
          </div>
          <!-- Shape settings -->
          <div
            class="bg-white border rounded p-3"
            id="shapeControls"
            style="display: none"
          >
            <h3 class="font-medium mb-2">Shape Settings</h3>

            <!-- Line Style -->
            <div class="mb-3">
              <label class="text-sm text-slate-600 block mb-2">Line Style</label>
              <div class="inline-flex border rounded overflow-hidden" role="group">
                <button type="button" id="lineStyleSolid" class="px-3 py-2 hover:bg-slate-100 border-r" title="Solid Line">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <button type="button" id="lineStyleDashed" class="px-3 py-2 hover:bg-slate-100 border-r" title="Dashed Line">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="4,2"/>
                  </svg>
                </button>
                <button type="button" id="lineStyleDotted" class="px-3 py-2 hover:bg-slate-100 border-r" title="Dotted Line">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="1,2"/>
                  </svg>
                </button>
                <button type="button" id="lineStyleDashDot" class="px-3 py-2 hover:bg-slate-100" title="Dash-Dot Line">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2" stroke-dasharray="6,2,1,2"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Line Thickness -->
            <div class="mb-3">
              <label class="text-sm text-slate-600 block mb-2">Line Thickness</label>
              <div class="inline-flex border rounded overflow-hidden" role="group">
                <button type="button" id="thickness1" class="px-3 py-2 hover:bg-slate-100 border-r" title="1px">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="1"/>
                  </svg>
                </button>
                <button type="button" id="thickness2" class="px-3 py-2 hover:bg-slate-100 border-r" title="2px">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
                <button type="button" id="thickness3" class="px-3 py-2 hover:bg-slate-100 border-r" title="3px">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="3"/>
                  </svg>
                </button>
                <button type="button" id="thickness4" class="px-3 py-2 hover:bg-slate-100 border-r" title="4px">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="4"/>
                  </svg>
                </button>
                <button type="button" id="thickness5" class="px-3 py-2 hover:bg-slate-100" title="5px">
                  <svg width="24" height="12" viewBox="0 0 24 12" class="w-6 h-3">
                    <line x1="2" y1="6" x2="22" y2="6" stroke="currentColor" stroke-width="5"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Fill Style -->
            <div class="mb-3">
              <label class="text-sm text-slate-600 block mb-2">Fill</label>
              <div class="inline-flex border rounded overflow-hidden" role="group">
                <button type="button" id="fillNone" class="px-3 py-2 hover:bg-slate-100 border-r" title="No Fill">
                  <svg width="20" height="20" viewBox="0 0 20 20" class="w-5 h-5">
                    <rect x="2" y="2" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1"/>
                    <line x1="2" y1="18" x2="18" y2="2" stroke="red" stroke-width="2"/>
                  </svg>
                </button>
                <button type="button" id="fillSolid" class="px-3 py-2 hover:bg-slate-100" title="Solid Fill">
                  <svg width="20" height="20" viewBox="0 0 20 20" class="w-5 h-5">
                    <rect x="2" y="2" width="16" height="16" fill="currentColor" stroke="currentColor" stroke-width="1"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="text-xs text-slate-500 mt-2">Click options to apply to selected shape immediately.</div>
          </div>
          <!-- Image settings -->
          <div
            class="bg-white border rounded p-3"
            id="imgControls"
            style="display: none"
          >
            <h3 class="font-medium mb-2">Image Settings</h3>
            <div class="flex items-center gap-3">
              <label class="text-sm text-slate-600">Threshold</label>
              <input
                id="imgThreshold"
                type="range"
                min="0"
                max="255"
                step="1"
                value="180"
                class="w-48"
              />
              <label
                class="text-sm text-slate-600 inline-flex items-center gap-1"
                ><input id="imgDither" type="checkbox" /> Dither</label
              >
            </div>
            <div class="text-xs text-slate-500 mt-1">
              Adjust B/W for selected image
            </div>
          </div>

          <!-- Counter settings -->
          <div
            class="bg-white border rounded p-3"
            id="counterControls"
            style="display: none"
          >
            <h3 class="font-medium mb-2">Counter Settings</h3>
            <div class="grid grid-cols-2 gap-3 mb-3">
              <div>
                <label class="text-sm text-slate-600 block mb-1">Start Number</label>
                <input
                  id="counterStart"
                  type="number"
                  min="0"
                  max="99999"
                  value="1"
                  class="w-full px-2 py-1 border rounded text-sm"
                />
              </div>
              <div>
                <label class="text-sm text-slate-600 block mb-1">Padding (Digits)</label>
                <select id="counterPadding" class="w-full px-2 py-1 border rounded text-sm">
                  <option value="1">1 (1, 2, 3...)</option>
                  <option value="2">2 (01, 02, 03...)</option>
                  <option value="3" selected>3 (001, 002, 003...)</option>
                  <option value="4">4 (0001, 0002, 0003...)</option>
                  <option value="5">5 (00001, 00002, 00003...)</option>
                </select>
              </div>
            </div>
            <div class="mb-3">
              <label class="text-sm text-slate-600 block mb-2">Counter Mode</label>
              <div class="space-y-2">
                <label class="inline-flex items-center">
                  <input type="radio" name="counterMode" value="data" checked class="mr-2">
                  <span class="text-sm">By Data Records</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="radio" name="counterMode" value="print" class="mr-2">
                  <span class="text-sm">By Print Sequence</span>
                </label>
              </div>
              <div class="text-xs text-slate-500 mt-1">
                <div><strong>By Data Records:</strong> 3 data, 2 copies → 10,10, 11,11, 12,12</div>
                <div><strong>By Print Sequence:</strong> 3 data, 2 copies → 10, 11, 12, 13, 14, 15 (ignores collation)</div>
              </div>
            </div>
            <div class="text-xs text-slate-500 mt-3">
              Counter increases by 1 for each record in Data JSON.<br>
              Settings are applied automatically when changed.
            </div>
          </div>

          <!-- Data under label area -->
          <div class="bg-white border rounded p-3">
            <h3 class="font-medium mb-2">Data (JSON Array)</h3>
            <div class="flex flex-col gap-2">
              <textarea
                id="dataJSON"
                rows="6"
                class="border rounded p-2 font-mono text-xs"
                placeholder='[
  { "uic": "XX010101", "id_card": "1234123412340" },
  { "uic": "YY010101", "id_card": "1234123412341" }
]'
              ></textarea>
              <div class="flex items-center gap-3 flex-wrap">
                <label class="text-sm text-slate-600">Preview row</label>
                <input
                  id="dataIndex"
                  type="number"
                  value="1"
                  min="1"
                  step="1"
                  class="border rounded px-2 py-1 w-20"
                />
                <button
                  id="previewData"
                  class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300"
                >
                  Apply Preview
                </button>
                <span id="dataStatus" class="text-xs text-slate-500"></span>
              </div>
            </div>
          </div>
          <!-- Bindings under data -->
          <div class="bg-white border rounded p-3">
            <h3 class="font-medium mb-2">Bindings (All Elements)</h3>
            <div
              id="bindingsList"
              class="flex flex-col gap-2 max-h-56 overflow-auto"
            ></div>
            <div class="text-xs text-slate-500 mt-1">
              Tip: Paste JSON, click Apply Preview to test bindings.
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const DPI = 203;
      let WIDTH_DOTS = 480,
          HEIGHT_DOTS = 160,
          CURRENT_WIDTH_MM = 60,
          CURRENT_HEIGHT_MM = 20,
          CURRENT_ORIENTATION = 'portrait'; // 'portrait' or 'landscape'

      const stage = new fabric.Canvas('stage', {
        backgroundColor: '#fff',
        selection: true,
        preserveObjectStacking: true,
      });
      stage.setWidth(WIDTH_DOTS);
      stage.setHeight(HEIGHT_DOTS);

      // Configure FabricJS handle display settings for better visibility
      fabric.Object.prototype.set({
        cornerSize: 12,
        cornerColor: '#2563eb',
        cornerStrokeColor: '#ffffff',
        borderColor: '#2563eb',
        transparentCorners: false,
        cornerStyle: 'rect'
      });

      // Configure FabricJS for high-DPI displays (MacBook Air M1, etc.)
      fabric.devicePixelRatio = window.devicePixelRatio || 1;

      // Ensure proper canvas scaling for high-DPI displays
      stage.enableRetinaScaling = true;

      // Paper size configurations
      const PAPER_SIZES = {
        '60x20': { width: 60, height: 20 },
        '50x20': { width: 50, height: 20 },
        '50x30': { width: 50, height: 30 },
        '40x30': { width: 40, height: 30 },
        '75x130': { width: 75, height: 130 }
      };

      function mmToDots(mm) {
        return Math.round(mm * DPI / 25.4);
      }

      function updateCanvasSize(widthMm, heightMm) {
        // Check if there are objects on the canvas
        if (stage.getObjects().length > 0) {
          const confirmed = confirm(
            'Changing paper size will clear all elements on the label. Do you want to continue?'
          );
          if (!confirmed) {
            // Revert the paper size selection
            revertPaperSizeSelection();
            return;
          }
          // Clear all objects
          clearCanvas();
        }

        CURRENT_WIDTH_MM = widthMm;
        CURRENT_HEIGHT_MM = heightMm;

        // Update canvas size based on current orientation
        updateCanvasSizeForOrientation();

        // Update orientation info
        updateOrientationInfo();

        // Ensure zoom is applied immediately after canvas size change
        requestAnimationFrame(() => {
          const MAX_DISPLAY_WIDTH = 500;
          const aspectRatio = HEIGHT_DOTS / WIDTH_DOTS;
          const displayWidth = Math.min(WIDTH_DOTS, MAX_DISPLAY_WIDTH);
          const displayHeight = displayWidth * aspectRatio;
          applyCanvasDisplaySize(displayWidth, displayHeight);
        });

        // Take snapshot for undo
        snapshot();
      }

      function handlePaperSizeChange() {
        const select = document.getElementById('paperSize');
        const customInputs = document.getElementById('customSizeInputs');

        if (select.value === 'custom') {
          customInputs.classList.remove('hidden');
        } else {
          customInputs.classList.add('hidden');
          const size = PAPER_SIZES[select.value];
          if (size) {
            updateCanvasSize(size.width, size.height);
          }
        }
      }

      function applyCustomSize() {
        const width = parseInt(document.getElementById('customWidth').value) || 60;
        const height = parseInt(document.getElementById('customHeight').value) || 20;

        // Validate size limits
        const minSize = 10, maxSize = 200;
        const validWidth = Math.max(minSize, Math.min(maxSize, width));
        const validHeight = Math.max(minSize, Math.min(maxSize, height));

        // Update input values if they were corrected
        document.getElementById('customWidth').value = validWidth;
        document.getElementById('customHeight').value = validHeight;

        updateCanvasSize(validWidth, validHeight);
      }

      function setOrientation(orientation) {
        // Don't change if already the same orientation
        if (CURRENT_ORIENTATION === orientation) return;

        // Check if there are objects on the canvas
        if (stage.getObjects().length > 0) {
          const confirmed = confirm(
            'Changing orientation will clear all elements on the label. Do you want to continue?'
          );
          if (!confirmed) {
            // Keep current orientation button state
            return;
          }
          // Clear all objects
          clearCanvas();
        }

        CURRENT_ORIENTATION = orientation;

        // Update button states
        document.getElementById('orientationPortrait').classList.remove('bg-blue-500', 'text-white');
        document.getElementById('orientationLandscape').classList.remove('bg-blue-500', 'text-white');

        if (orientation === 'portrait') {
          document.getElementById('orientationPortrait').classList.add('bg-blue-500', 'text-white');
        } else {
          document.getElementById('orientationLandscape').classList.add('bg-blue-500', 'text-white');
        }

        // Update canvas size based on orientation
        updateCanvasSizeForOrientation();

        // Update orientation info
        updateOrientationInfo();

        // Take snapshot for undo
        snapshot();
      }

      function updateCanvasSizeForOrientation() {
        const stageWrap = document.getElementById('stageWrap');
        const MAX_DISPLAY_WIDTH = 500; // Fixed maximum display width

        if (CURRENT_ORIENTATION === 'landscape') {
          // For landscape, swap the display dimensions
          const tempWidth = CURRENT_WIDTH_MM;
          const tempHeight = CURRENT_HEIGHT_MM;

          // Update canvas to show swapped dimensions
          WIDTH_DOTS = mmToDots(tempHeight);
          HEIGHT_DOTS = mmToDots(tempWidth);

          stage.setWidth(WIDTH_DOTS);
          stage.setHeight(HEIGHT_DOTS);

          const canvasEl = document.getElementById('stage');
          canvasEl.width = WIDTH_DOTS;
          canvasEl.height = HEIGHT_DOTS;

          // Calculate display size to fit within max width, maintaining aspect ratio
          const aspectRatio = HEIGHT_DOTS / WIDTH_DOTS; // height / width
          let displayWidth = Math.min(WIDTH_DOTS, MAX_DISPLAY_WIDTH);
          let displayHeight = displayWidth * aspectRatio;

          // Apply display size using FabricJS zoom (handles stageWrap sizing internally)
          // Force canvas render first, then apply zoom
          stage.requestRenderAll();
          requestAnimationFrame(() => {
            applyCanvasDisplaySize(displayWidth, displayHeight);
          });

          // Update title to show swapped dimensions
          document.querySelector('h1').textContent = `Label Editor (${tempHeight}×${tempWidth} mm @203dpi)`;
        } else {
          // For portrait, use normal dimensions
          WIDTH_DOTS = mmToDots(CURRENT_WIDTH_MM);
          HEIGHT_DOTS = mmToDots(CURRENT_HEIGHT_MM);

          stage.setWidth(WIDTH_DOTS);
          stage.setHeight(HEIGHT_DOTS);

          const canvasEl = document.getElementById('stage');
          canvasEl.width = WIDTH_DOTS;
          canvasEl.height = HEIGHT_DOTS;

          // Calculate display size to fit within max width, maintaining aspect ratio
          const aspectRatio = HEIGHT_DOTS / WIDTH_DOTS; // height / width
          let displayWidth = Math.min(WIDTH_DOTS, MAX_DISPLAY_WIDTH);
          let displayHeight = displayWidth * aspectRatio;

          // Apply display size using FabricJS zoom (handles stageWrap sizing internally)
          // Force canvas render first, then apply zoom
          stage.requestRenderAll();
          requestAnimationFrame(() => {
            applyCanvasDisplaySize(displayWidth, displayHeight);
          });

          // Update title to show normal dimensions
          document.querySelector('h1').textContent = `Label Editor (${CURRENT_WIDTH_MM}×${CURRENT_HEIGHT_MM} mm @203dpi)`;
        }

        stage.requestRenderAll();
      }

      function applyCanvasDisplaySize(displayWidth, displayHeight) {
        // Store display size for later use
        stage._displayWidth = displayWidth;
        stage._displayHeight = displayHeight;

        // Reset zoom and viewport transform to get accurate canvas dimensions
        stage.setZoom(1);
        stage.setViewportTransform([1, 0, 0, 1, 0, 0]);

        // Get actual canvas dimensions
        const actualWidth = stage.getWidth();
        const actualHeight = stage.getHeight();

        // Calculate zoom factor to fit within display size
        const zoomX = displayWidth / actualWidth;
        const zoomY = displayHeight / actualHeight;
        const zoom = Math.min(zoomX, zoomY);

        // Apply FabricJS zoom instead of CSS scaling
        stage.setZoom(zoom);

        // Adjust handle size based on zoom level to maintain consistent visual size
        const baseCornerSize = 12;
        const adjustedCornerSize = Math.max(8, Math.min(20, baseCornerSize / zoom));

        // Update all existing objects with the new corner size
        stage.getObjects().forEach(obj => {
          obj.set('cornerSize', adjustedCornerSize);
        });

        // Update the default for new objects
        fabric.Object.prototype.cornerSize = adjustedCornerSize;

        // Calculate actual displayed size after zoom
        const zoomedWidth = actualWidth * zoom;
        const zoomedHeight = actualHeight * zoom;

        // Update container size to match zoomed canvas
        const stageWrap = document.getElementById('stageWrap');
        stageWrap.style.width = Math.round(zoomedWidth) + 'px';
        stageWrap.style.height = Math.round(zoomedHeight) + 'px';

        // Remove any CSS scaling from canvas elements - let FabricJS zoom handle everything
        const canvasElements = document.querySelectorAll('#stageWrap canvas');
        canvasElements.forEach(canvas => {
          canvas.style.width = '';
          canvas.style.height = '';
          canvas.style.maxWidth = '';
          canvas.style.display = 'block';
        });

        // Also remove CSS scaling from main canvas
        const mainCanvas = document.getElementById('stage');
        if (mainCanvas) {
          mainCanvas.style.width = '';
          mainCanvas.style.height = '';
          mainCanvas.style.maxWidth = '';
        }

        // Debug logging for large canvases
        if (actualWidth > 500 || actualHeight > 500) {
          console.log('Large canvas detected:', actualWidth, 'x', actualHeight);
          console.log('Zoom factor:', zoom);
          console.log('Display size:', zoomedWidth, 'x', zoomedHeight);
          console.log('Adjusted corner size:', adjustedCornerSize);
        }

        // Force re-render to update handle positions
        stage.requestRenderAll();
      }

      // Helper function to apply zoom-adjusted corner size to new objects
      function applyZoomAdjustedCornerSize(obj) {
        const currentZoom = stage.getZoom();
        const baseCornerSize = 12;
        const adjustedCornerSize = Math.max(8, Math.min(20, baseCornerSize / currentZoom));
        obj.set('cornerSize', adjustedCornerSize);
      }

      function updateOrientationInfo() {
        const info = document.getElementById('orientationInfo');
        if (CURRENT_ORIENTATION === 'portrait') {
          info.textContent = `Portrait: ${CURRENT_WIDTH_MM}×${CURRENT_HEIGHT_MM} mm (W×H)`;
        } else {
          info.textContent = `Landscape: ${CURRENT_HEIGHT_MM}×${CURRENT_WIDTH_MM} mm (W×H) - Display rotated`;
        }
      }

      function getActualPaperSize() {
        // Always return the actual paper size (not swapped)
        return { width: CURRENT_WIDTH_MM, height: CURRENT_HEIGHT_MM };
      }

      function clearCanvas() {
        // Remove all objects from canvas
        stage.getObjects().slice().forEach((o) => stage.remove(o));
        stage.discardActiveObject();
        stage.requestRenderAll();
      }

      function revertPaperSizeSelection() {
        // Find the current paper size and revert selection
        const paperSizeSelect = document.getElementById('paperSize');
        const customInputs = document.getElementById('customSizeInputs');

        // Check if current size matches any standard size
        let matchedStandardSize = null;
        for (const [sizeKey, sizeValue] of Object.entries(PAPER_SIZES)) {
          if (sizeValue.width === CURRENT_WIDTH_MM && sizeValue.height === CURRENT_HEIGHT_MM) {
            matchedStandardSize = sizeKey;
            break;
          }
        }

        if (matchedStandardSize) {
          paperSizeSelect.value = matchedStandardSize;
          customInputs.classList.add('hidden');
        } else {
          paperSizeSelect.value = 'custom';
          customInputs.classList.remove('hidden');
          document.getElementById('customWidth').value = CURRENT_WIDTH_MM;
          document.getElementById('customHeight').value = CURRENT_HEIGHT_MM;
        }
      }

      // --- Simple undo history ---
      const TPL_KEY = 'a70pro_templates_plain';
      const HISTORY_LIMIT = 50;
      let history = [];
      let historyLock = false;
      function stageJSON() {
        return stage.toJSON([
          'selectable',
          '_bindingKey',
          '_kind',
          '_templateText',
          '_templateValue',
          '_srcOrig',
          '_bwThreshold',
          '_bwDither',
          '_bwManaged',
          '_isCounterField',
          '_isDateField',
          '_isTimeField',
          '_counterStart',
          '_counterPadding',
          '_counterMode',
        ]);
      }
      function snapshot() {
        if (historyLock) return;
        const json = JSON.stringify(stageJSON());
        if (!history.length || history[history.length - 1] !== json) {
          history.push(json);
          if (history.length > HISTORY_LIMIT) history.shift();
        }
      }
      function undo() {
        if (history.length <= 1) return; // nothing to undo
        history.pop();
        const prev = history[history.length - 1];
        historyLock = true;
        stage.loadFromJSON(prev, () => {
          stage.renderAll();
          historyLock = false;
          updateActionButtons();
        });
      }

      function createPrintCanvas() {
        // Get the current stage canvas
        const stageCanvas = stage.getElement();

        if (CURRENT_ORIENTATION === 'landscape') {
          // For landscape, create a rotated canvas with actual paper dimensions
          const actualWidth = mmToDots(CURRENT_WIDTH_MM);
          const actualHeight = mmToDots(CURRENT_HEIGHT_MM);

          const printCanvas = document.createElement('canvas');
          printCanvas.width = actualWidth;
          printCanvas.height = actualHeight;

          const printCtx = printCanvas.getContext('2d');
          printCtx.fillStyle = 'white';
          printCtx.fillRect(0, 0, actualWidth, actualHeight);

          // Rotate the content 90 degrees clockwise
          printCtx.translate(actualWidth / 2, actualHeight / 2);
          printCtx.rotate(Math.PI / 2);
          printCtx.translate(-stageCanvas.width / 2, -stageCanvas.height / 2);

          // Draw the stage content
          printCtx.drawImage(stageCanvas, 0, 0);

          return printCanvas;
        } else {
          // For portrait, return the stage canvas as-is
          return stageCanvas;
        }
      }

      function createPrintCanvasFromStageCanvas(stageCanvas, direction = 0) {
        // Handle direction rotation first (if needed)
        let sourceCanvas = stageCanvas;
        if (direction === 1) {
          const rotCanvas = document.createElement('canvas');
          rotCanvas.width = stageCanvas.width;
          rotCanvas.height = stageCanvas.height;
          const rotCtx = rotCanvas.getContext('2d');
          rotCtx.translate(stageCanvas.width, stageCanvas.height);
          rotCtx.rotate(Math.PI);
          rotCtx.drawImage(stageCanvas, 0, 0);
          sourceCanvas = rotCanvas;
        }

        if (CURRENT_ORIENTATION === 'landscape') {
          // For landscape, create a rotated canvas with actual paper dimensions
          const actualWidth = mmToDots(CURRENT_WIDTH_MM);
          const actualHeight = mmToDots(CURRENT_HEIGHT_MM);

          const printCanvas = document.createElement('canvas');
          printCanvas.width = actualWidth;
          printCanvas.height = actualHeight;

          const printCtx = printCanvas.getContext('2d');
          printCtx.fillStyle = 'white';
          printCtx.fillRect(0, 0, actualWidth, actualHeight);

          // Rotate the content 90 degrees clockwise
          printCtx.translate(actualWidth / 2, actualHeight / 2);
          printCtx.rotate(Math.PI / 2);
          printCtx.translate(-sourceCanvas.width / 2, -sourceCanvas.height / 2);

          // Draw the source content
          printCtx.drawImage(sourceCanvas, 0, 0);

          return printCanvas;
        } else {
          // For portrait, return the source canvas
          return sourceCanvas;
        }
      }

      function pack1bppFromCanvas(canvas, threshold = 200) {
        const w = canvas.width,
          h = canvas.height;
        const ctx = canvas.getContext('2d');
        const img = ctx.getImageData(0, 0, w, h).data;
        const bytesPerRow = Math.ceil(w / 8);
        const out = new Uint8Array(bytesPerRow * h);
        for (let y = 0; y < h; y++) {
          for (let xb = 0; xb < bytesPerRow; xb++) {
            let b = 0;
            for (let bit = 0; bit < 8; bit++) {
              const x = xb * 8 + bit;
              let v = 0;
              if (x < w) {
                const idx = (y * w + x) * 4;
                const r = img[idx],
                  g = img[idx + 1],
                  bl = img[idx + 2];
                const gray = 0.299 * r + 0.587 * g + 0.114 * bl;
                v = gray < threshold ? 1 : 0;
              }
              b |= v << (7 - bit);
            }
            out[y * bytesPerRow + xb] = b;
          }
        }
        return out;
      }

      // Convert an HTMLImageElement to a monochrome canvas (preview only)
      function toMonochromeCanvas(imgEl, threshold = 180, dither = false) {
        const w = imgEl.naturalWidth || imgEl.width;
        const h = imgEl.naturalHeight || imgEl.height;
        const c = document.createElement('canvas');
        c.width = w;
        c.height = h;
        const ctx = c.getContext('2d');
        // Fill white background first so transparent areas become white
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, w, h);
        ctx.drawImage(imgEl, 0, 0, w, h);
        const im = ctx.getImageData(0, 0, w, h);
        const data = im.data;
        if (dither) {
          const lum = new Float32Array(w * h);
          for (let i = 0, p = 0; i < data.length; i += 4, p++) {
            const a = data[i + 3];
            if (a < 128) lum[p] = 255; // treat transparent as white
            else
              lum[p] =
                0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
          }
          for (let y = 0; y < h; y++) {
            for (let x = 0; x < w; x++) {
              const p = y * w + x;
              const old = lum[p];
              const newVal = old < threshold ? 0 : 255;
              const err = old - newVal;
              lum[p] = newVal;
              if (x + 1 < w)
                lum[p + 1] = Math.max(
                  0,
                  Math.min(255, lum[p + 1] + (err * 7) / 16)
                );
              if (x - 1 >= 0 && y + 1 < h)
                lum[p + w - 1] = Math.max(
                  0,
                  Math.min(255, lum[p + w - 1] + (err * 3) / 16)
                );
              if (y + 1 < h)
                lum[p + w] = Math.max(
                  0,
                  Math.min(255, lum[p + w] + (err * 5) / 16)
                );
              if (x + 1 < w && y + 1 < h)
                lum[p + w + 1] = Math.max(
                  0,
                  Math.min(255, lum[p + w + 1] + (err * 1) / 16)
                );
            }
          }
          for (let y = 0; y < h; y++) {
            for (let x = 0; x < w; x++) {
              const p = y * w + x;
              const v = lum[p] < threshold ? 0 : 255;
              const i = p * 4;
              data[i] = data[i + 1] = data[i + 2] = v;
              data[i + 3] = 255;
            }
          }
        } else {
          for (let i = 0; i < data.length; i += 4) {
            const a = data[i + 3];
            let v;
            if (a < 128) {
              v = 255; // transparent -> white
            } else {
              const gray =
                0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
              v = gray < threshold ? 0 : 255;
            }
            data[i] = data[i + 1] = data[i + 2] = v;
            data[i + 3] = 255;
          }
        }
        ctx.putImageData(im, 0, 0);
        return c;
      }

      function addText(text = 'ทดสอบ / HELLO') {
        const familySel = document.getElementById('fontFamily').value;
        const family =
          familySel === 'system'
            ? 'sans-serif'
            : `${familySel}, Noto Sans Thai, sans-serif`;
        const size = Math.max(
          8,
          parseInt(document.getElementById('fontSize').value, 10) || 24
        );
        const weight = Math.max(
          100,
          Math.min(
            900,
            parseInt(document.getElementById('fontWeight').value, 10) || 600
          )
        );
        const align = (
          document.getElementById('textAlign')?.value || 'left'
        ).toLowerCase();
        const obj = new fabric.Textbox(text, {
          left: 5,
          top: 5,
          fontFamily: family,
          fontSize: size,
          fontWeight: String(weight),
          textAlign: ['left', 'center', 'right'].includes(align)
            ? align
            : 'left',
          fill: '#000',
          editable: true,
        });
        obj._templateText = text;
        obj._bindingKey = '';
        applyZoomAdjustedCornerSize(obj);
        stage.add(obj).setActiveObject(obj);
        updateActionButtons();
        snapshot();
      }

      function loadScript(src) {
        return new Promise((resolve, reject) => {
          const s = document.createElement('script');
          s.src = src;
          s.async = true;
          s.onload = () => resolve(src);
          s.onerror = (e) => reject(e);
          document.head.appendChild(s);
        });
      }
      async function tryLoadScripts(sources) {
        let lastErr;
        for (const src of sources) {
          try {
            await loadScript(src);
            return src;
          } catch (e) {
            lastErr = e;
          }
        }
        throw lastErr || new Error('All sources failed');
      }
      async function ensureQRCode() {
        if (window.qrcode) return;
        await tryLoadScripts(['./lib/qrcode.min.js', './qrcode.min.js']);
        if (!window.qrcode)
          throw new Error(
            'QR library failed to load (place qrcode.min.js in examples/lib).'
          );
      }

      // --- Local Code128-B drawer (no CDN) ---
      const CODE128_PATTERNS = [
        '212222',
        '222122',
        '222221',
        '121223',
        '121322',
        '131222',
        '122213',
        '122312',
        '132212',
        '221213',
        '221312',
        '231212',
        '112232',
        '122132',
        '122231',
        '113222',
        '123122',
        '123221',
        '223211',
        '221132',
        '221231',
        '213212',
        '223112',
        '312131',
        '311222',
        '321122',
        '321221',
        '312212',
        '322112',
        '322211',
        '212123',
        '212321',
        '232121',
        '111323',
        '131123',
        '131321',
        '112313',
        '132113',
        '132311',
        '211313',
        '231113',
        '231311',
        '112133',
        '112331',
        '132131',
        '113123',
        '113321',
        '133121',
        '313121',
        '211331',
        '231131',
        '213113',
        '213311',
        '213131',
        '311123',
        '311321',
        '331121',
        '312113',
        '312311',
        '332111',
        '314111',
        '221411',
        '431111',
        '111224',
        '111422',
        '121124',
        '121421',
        '141122',
        '141221',
        '112214',
        '112412',
        '122114',
        '122411',
        '142112',
        '142211',
        '241211',
        '221114',
        '413111',
        '241112',
        '134111',
        '111242',
        '121142',
        '121241',
        '114212',
        '124112',
        '124211',
        '411212',
        '421112',
        '421211',
        '212141',
        '214121',
        '412121',
        '111143',
        '111341',
        '131141',
        '114113',
        '114311',
        '411113',
        '411311',
        '113141',
        '114131',
        '311141',
        '411131',
        '211412',
        '211214',
        '211232',
        '2331112',
      ];
      const START_B = 104,
        STOP = 106;
      function code128BValues(text) {
        const vals = [START_B];
        for (let i = 0; i < text.length; i++) {
          const c = text.charCodeAt(i);
          if (c < 32 || c > 126)
            throw new Error('Code128-B expects ASCII 32..126');
          vals.push(c - 32);
        }
        let sum = START_B;
        for (let i = 1; i < vals.length; i++) sum += vals[i] * i;
        vals.push(sum % 103);
        vals.push(STOP);
        return vals;
      }
      function drawCode128ToCanvas(canvas, text, moduleW = 2, height = 70) {
        const ctx = canvas.getContext('2d');
        const vals = code128BValues(text);
        let width = 0;
        for (const v of vals) {
          for (const ch of CODE128_PATTERNS[v])
            width += parseInt(ch, 10) * moduleW;
        }
        canvas.width = Math.max(2, width);
        canvas.height = height;
        ctx.fillStyle = '#FFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#000';
        let x = 0;
        for (const v of vals) {
          const pattern = CODE128_PATTERNS[v];
          let bar = true;
          for (let i = 0; i < pattern.length; i++) {
            const w = parseInt(pattern[i], 10) * moduleW;
            if (bar) ctx.fillRect(x, 0, w, height);
            x += w;
            bar = !bar;
          }
        }
      }
      function updateBarcodeObject(oldObj, value) {
        const tmp = document.createElement('canvas');
        try {
          drawCode128ToCanvas(tmp, value, 2, 70);
        } catch (e) {
          drawCode128ToCanvas(tmp, String(value || ''), 2, 70);
        }
        const props = {
          left: oldObj.left,
          top: oldObj.top,
          scaleX: oldObj.scaleX,
          scaleY: oldObj.scaleY,
          angle: oldObj.angle,
          selectable: true,
        };
        const img = new fabric.Image(tmp, props);
        img._kind = 'barcode';
        img._bindingKey = oldObj._bindingKey || '';
        img._templateValue = String(value || '');
        stage.remove(oldObj);
        stage.add(img).setActiveObject(img);
        stage.requestRenderAll();
        return img;
      }
      async function addBarcode(value = '1234567890') {
        const tmp = document.createElement('canvas');
        drawCode128ToCanvas(tmp, value, 2, 70);
        const img = new fabric.Image(tmp, {
          left: 5,
          top: 5,
          selectable: true,
        });
        img._kind = 'barcode';
        img._templateValue = String(value || '');
        img._bindingKey = '';
        stage.add(img).setActiveObject(img);
        updateActionButtons();
      }

      function updateQRObject(oldObj, text) {
        const modulesScale = 4,
          margin = 0;
        const qr = window.qrcode(0, 'M');
        qr.addData(String(text || ''));
        qr.make();
        const modules = qr.getModuleCount();
        const size = modules * modulesScale + margin * 2 * modulesScale;
        const tmp = document.createElement('canvas');
        tmp.width = size;
        tmp.height = size;
        const tctx = tmp.getContext('2d');
        tctx.fillStyle = '#FFFFFF';
        tctx.fillRect(0, 0, size, size);
        tctx.fillStyle = '#000000';
        for (let r = 0; r < modules; r++) {
          for (let c = 0; c < modules; c++) {
            if (qr.isDark(r, c))
              tctx.fillRect(
                margin * modulesScale + c * modulesScale,
                margin * modulesScale + r * modulesScale,
                modulesScale,
                modulesScale
              );
          }
        }
        const props = {
          left: oldObj.left,
          top: oldObj.top,
          scaleX: oldObj.scaleX,
          scaleY: oldObj.scaleY,
          angle: oldObj.angle,
          selectable: true,
        };
        const img = new fabric.Image(tmp, props);
        img._kind = 'qr';
        img._bindingKey = oldObj._bindingKey || '';
        img._templateValue = String(text || '');
        stage.remove(oldObj);
        stage.add(img).setActiveObject(img);
        stage.requestRenderAll();
        return img;
      }
      async function addQR(text = 'Hello') {
        await ensureQRCode();
        const qr = window.qrcode(0, 'M'); // auto size, medium EC
        qr.addData(String(text));
        qr.make();
        const modules = qr.getModuleCount();
        const scale = 4; // pixels per module
        const margin = 0; // no quiet zone
        const size = modules * scale + margin * 2 * scale;
        const tmp = document.createElement('canvas');
        tmp.width = size;
        tmp.height = size;
        const tctx = tmp.getContext('2d');
        tctx.fillStyle = '#FFFFFF';
        tctx.fillRect(0, 0, size, size);
        tctx.fillStyle = '#000000';
        for (let r = 0; r < modules; r++) {
          for (let c = 0; c < modules; c++) {
            if (qr.isDark(r, c))
              tctx.fillRect(
                margin * scale + c * scale,
                margin * scale + r * scale,
                scale,
                scale
              );
          }
        }
        const img = new fabric.Image(tmp, {
          left: 5,
          top: 5,
          selectable: true,
        });
        img._kind = 'qr';
        img._templateValue = String(text || '');
        img._bindingKey = '';
        stage.add(img).setActiveObject(img);
        updateActionButtons();
      }

      // --- Bindings list UI ---
      function objKind(o) {
        if (o._kind === 'barcode') return 'Barcode';
        if (o._kind === 'qr') return 'QR';
        const t = String(o.type || '');
        if (t.includes('text')) return 'Text';
        if (t.includes('line')) return 'Line';
        if (t.includes('rect')) return 'Rect';
        if (t.includes('circle')) return 'Circle';
        if (t.includes('triangle')) return 'Triangle';
        if (t.includes('image')) return 'Image';
        return t || 'Object';
      }
      function isBindable(o) {
        const k = objKind(o);
        return k === 'Text' || k === 'Barcode' || k === 'QR';
      }
      function getDataKeys() {
        try {
          const raw = (document.getElementById('dataJSON').value || '').trim();
          if (!raw) return [];
          const arr = JSON.parse(raw);
          if (!Array.isArray(arr) || arr.length === 0) return [];
          const max = Math.min(arr.length, 50);
          const keys = new Set();
          for (let i = 0; i < max; i++) {
            const rec = arr[i];
            if (rec && typeof rec === 'object') {
              Object.keys(rec).forEach((k) => keys.add(k));
            }
          }
          return Array.from(keys);
        } catch (_) {
          return [];
        }
      }
      function renderBindingsList() {
        const cont = document.getElementById('bindingsList');
        if (!cont) return;
        cont.innerHTML = '';
        const objs = stage.getObjects();
        if (!objs.length) {
          const d = document.createElement('div');
          d.className = 'text-sm text-slate-500';
          d.textContent = 'No elements. Add Text/Barcode/QR to bind.';
          cont.append(d);
          return;
        }
        const keys = getDataKeys();
        objs.forEach((o, idx) => {
          const row = document.createElement('div');
          row.className = 'flex items-center gap-2';
          const label = document.createElement('div');
          label.className = 'text-xs w-28 truncate';
          const kind = objKind(o);
          let preview = '';
          if (kind === 'Text') preview = (o.text || '').toString().slice(0, 20);
          else if (kind === 'Barcode' || kind === 'QR')
            preview = o._templateValue || '';
          label.textContent = `${String(idx + 1).padStart(2, '0')} • ${kind}`;
          label.title = preview ? `${kind}: ${preview}` : kind;
          if (isBindable(o)) {
            const sel = document.createElement('select');
            sel.className = 'border rounded px-2 py-1 w-48 text-sm';
            const none = document.createElement('option');
            none.value = '';
            none.textContent = keys.length ? '(none)' : 'Paste JSON above';
            sel.append(none);
            keys.forEach((k) => {
              const op = document.createElement('option');
              op.value = k;
              op.textContent = k;
              sel.append(op);
            });
            sel.value = o._bindingKey || '';
            sel.disabled = keys.length === 0;
            sel.onchange = () => {
              o._bindingKey = sel.value || '';
            };
            const btn = document.createElement('button');
            btn.className =
              'px-2 py-1 bg-slate-200 rounded hover:bg-slate-300 text-xs';
            btn.textContent = 'Select';
            btn.onclick = () => {
              stage.setActiveObject(o);
              stage.requestRenderAll();
            };
            row.append(label, sel, btn);
          } else {
            const note = document.createElement('div');
            note.className = 'text-xs text-slate-400';
            note.textContent = '(not bindable)';
            row.append(label, note);
          }
          cont.append(row);
        });
      }

      function addImage(file) {
        const reader = new FileReader();
        reader.onload = () => {
          const origSrc = reader.result;
          const tmp = new Image();
          tmp.crossOrigin = 'anonymous';
          tmp.src = origSrc;
          tmp.onload = () => {
            // Fit to label if too large; place at top-left
            const maxW = WIDTH_DOTS - 10,
              maxH = HEIGHT_DOTS - 10;
            let scale = 1;
            if (tmp.width > maxW || tmp.height > maxH)
              scale = Math.min(maxW / tmp.width, maxH / tmp.height);
            const monoCanvas = toMonochromeCanvas(tmp, 180, false);
            const img = new fabric.Image(monoCanvas, {
              left: 5,
              top: 5,
              selectable: true,
              scaleX: scale,
              scaleY: scale,
            });
            img._srcOrig = origSrc;
            img._bwThreshold = 180;
            img._bwDither = false;
            img._bwManaged = true;
            applyZoomAdjustedCornerSize(img);
            stage.add(img).setActiveObject(img);
            updateActionButtons();
            snapshot();
          };
        };
        reader.readAsDataURL(file);
      }

      // --- Shapes ---
      function currentStroke() {
        return 2; // Default stroke width
      }
      function addLine() {
        const line = new fabric.Line([0, 10, 120, 10], {
          left: 5,
          top: 5,
          stroke: '#000',
          strokeWidth: currentStroke(),
          strokeUniform: true,
          selectable: true,
        });
        applyZoomAdjustedCornerSize(line);
        stage.add(line).setActiveObject(line);
        updateActionButtons();
        snapshot();
      }
      function addRect() {
        const r = new fabric.Rect({
          left: 5,
          top: 5,
          width: 120,
          height: 60,
          fill: 'rgba(0,0,0,0)',
          stroke: '#000',
          strokeWidth: currentStroke(),
          strokeUniform: true,
          selectable: true,
        });
        applyZoomAdjustedCornerSize(r);
        stage.add(r).setActiveObject(r);
        updateActionButtons();
        snapshot();
      }
      function addCircle() {
        const c = new fabric.Circle({
          left: 5,
          top: 5,
          radius: 30,
          fill: 'rgba(0,0,0,0)',
          stroke: '#000',
          strokeWidth: currentStroke(),
          strokeUniform: true,
          selectable: true,
        });
        applyZoomAdjustedCornerSize(c);
        stage.add(c).setActiveObject(c);
        updateActionButtons();
        snapshot();
      }
      function addTriangle() {
        const t = new fabric.Triangle({
          left: 5,
          top: 5,
          width: 80,
          height: 60,
          fill: 'rgba(0,0,0,0)',
          stroke: '#000',
          strokeWidth: currentStroke(),
          strokeUniform: true,
          selectable: true,
        });
        applyZoomAdjustedCornerSize(t);
        stage.add(t).setActiveObject(t);
        updateActionButtons();
        snapshot();
      }

      function addDate() {
        const today = new Date();
        const dateStr = today.toLocaleDateString('th-TH', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });

        const obj = new fabric.Textbox(dateStr, {
          left: 5,
          top: 5,
          fontFamily: 'Sarabun, Noto Sans Thai, sans-serif',
          fontSize: 20,
          fontWeight: '600',
          fill: '#000',
          editable: true,
        });
        obj._templateText = dateStr;
        obj._bindingKey = '';
        obj._isDateField = true; // Mark as date field for auto-update
        applyZoomAdjustedCornerSize(obj);
        stage.add(obj).setActiveObject(obj);
        updateActionButtons();
        snapshot();
      }

      function addTime() {
        const now = new Date();
        const timeStr = now.toLocaleTimeString('th-TH', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });

        const obj = new fabric.Textbox(timeStr, {
          left: 5,
          top: 5,
          fontFamily: 'Sarabun, Noto Sans Thai, sans-serif',
          fontSize: 20,
          fontWeight: '600',
          fill: '#000',
          editable: true,
        });
        obj._templateText = timeStr;
        obj._bindingKey = '';
        obj._isTimeField = true; // Mark as time field for auto-update
        applyZoomAdjustedCornerSize(obj);
        stage.add(obj).setActiveObject(obj);
        updateActionButtons();
        snapshot();
      }

      function addCounter() {
        const obj = new fabric.Textbox('001', {
          left: 5,
          top: 5,
          fontFamily: 'Sarabun, Noto Sans Thai, sans-serif',
          fontSize: 24,
          fontWeight: '700',
          fill: '#000',
          editable: true,
        });
        obj._templateText = '001';
        obj._bindingKey = '';
        obj._isCounterField = true; // Mark as counter field
        obj._counterStart = 1; // Starting number
        obj._counterPadding = 3; // Zero padding (001, 002, etc.)
        obj._counterMode = 'data'; // 'data' or 'print'
        applyZoomAdjustedCornerSize(obj);
        stage.add(obj).setActiveObject(obj);
        updateActionButtons();
        snapshot();
      }

      document.getElementById('addText').onclick = () =>
        addText(prompt('Text:', 'ทดสอบ / HELLO') || '');
      document.getElementById('addBarcode').onclick = async () => {
        await addBarcode(prompt('Code128 value:', '1234567890') || '');
      };
      document.getElementById('addQR').onclick = async () => {
        try {
          await addQR(prompt('QR text:', 'Hello from A70Pro') || '');
        } catch (e) {
          alert(
            'QR library failed to load. Check internet connection or add a local qrcode.min.js in examples/lib.'
          );
        }
      };
      document.getElementById('imgFile').onchange = (e) => {
        const f = e.target.files && e.target.files[0];
        if (f) addImage(f);
      };
      document.getElementById('addLine').onclick = addLine;
      document.getElementById('addRect').onclick = addRect;
      document.getElementById('addCircle').onclick = addCircle;
      document.getElementById('addTriangle').onclick = addTriangle;
      document.getElementById('addDate').onclick = addDate;
      document.getElementById('addTime').onclick = addTime;
      document.getElementById('addCounter').onclick = addCounter;

      function updateCounterSettings() {
        const ao = stage.getActiveObject();
        if (ao && ao._isCounterField) {
          const startValue = parseInt(document.getElementById('counterStart').value) || 1;
          const paddingValue = parseInt(document.getElementById('counterPadding').value) || 3;
          const counterMode = document.querySelector('input[name="counterMode"]:checked')?.value || 'data';

          ao._counterStart = startValue;
          ao._counterPadding = paddingValue;
          ao._counterMode = counterMode;

          // Update the display text to show the new format
          const sampleNumber = startValue.toString().padStart(paddingValue, '0');
          ao.text = sampleNumber;

          stage.requestRenderAll();
          snapshot();
        }
      }
      function updateActionButtons() {
        const hasSel = !!stage.getActiveObject();
        const copyBtn = document.getElementById('copy');
        const delBtn = document.getElementById('del');
        const frontBtn = document.getElementById('bringFront');
        const backBtn = document.getElementById('sendBack');
        const saveBtn = document.getElementById('saveTpl');
        copyBtn.disabled = !hasSel;
        delBtn.disabled = !hasSel;
        if (frontBtn) frontBtn.disabled = !hasSel;
        if (backBtn) backBtn.disabled = !hasSel;
        copyBtn.className = hasSel
          ? 'px-3 py-2 rounded bg-emerald-600 text-white hover:bg-emerald-500 inline-flex items-center gap-2'
          : 'px-3 py-2 rounded bg-slate-200 text-slate-400 cursor-not-allowed inline-flex items-center gap-2';
        delBtn.className = hasSel
          ? 'px-3 py-2 rounded bg-rose-600 text-white hover:bg-rose-500 inline-flex items-center gap-2'
          : 'px-3 py-2 rounded bg-slate-200 text-slate-400 cursor-not-allowed inline-flex items-center gap-2';
        if (frontBtn)
          frontBtn.className = hasSel
            ? 'px-3 py-2 rounded bg-slate-200 hover:bg-slate-300 inline-flex items-center gap-2'
            : 'px-3 py-2 rounded bg-slate-200 text-slate-400 cursor-not-allowed inline-flex items-center gap-2';
        if (backBtn)
          backBtn.className = hasSel
            ? 'px-3 py-2 rounded bg-slate-200 hover:bg-slate-300 inline-flex items-center gap-2'
            : 'px-3 py-2 rounded bg-slate-200 text-slate-400 cursor-not-allowed inline-flex items-center gap-2';
        // Also disable Print if no objects on canvas
        const btn = document.getElementById('print');
        const objCount = stage.getObjects().length;
        // Save disabled when canvas empty
        const canSave = objCount > 0;
        saveBtn.disabled = !canSave;
        saveBtn.className = canSave
          ? 'px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 inline-flex items-center gap-2'
          : 'px-3 py-2 rounded bg-slate-200 text-slate-400 cursor-not-allowed inline-flex items-center gap-2';
        const online = (window.__printersState || {})[
          (document.getElementById('printer').value || '').trim()
        ];
        const printerOnline = online === 'idle' || online === 'printing';
        const enabled = printerOnline && objCount > 0;
        btn.disabled = !enabled;
        btn.className = enabled
          ? 'px-5 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 text-base font-semibold shadow inline-flex items-center gap-2'
          : 'px-5 py-3 bg-slate-300 text-slate-500 rounded text-base font-semibold shadow inline-flex items-center gap-2 cursor-not-allowed';
      }
      function onSelectionChange() {
        updateActionButtons();
        const ao = stage.getActiveObject();
        // Image controls visibility
        const imgCtrl = document.getElementById('imgControls');
        if (ao && ao._bwManaged) {
          imgCtrl.style.display = '';
          document.getElementById('imgThreshold').value =
            ao._bwThreshold || 180;
          document.getElementById('imgDither').checked = !!ao._bwDither;
        } else {
          imgCtrl.style.display = 'none';
        }

        // Counter controls visibility
        const counterCtrl = document.getElementById('counterControls');
        if (ao && ao._isCounterField) {
          counterCtrl.style.display = 'block';
          document.getElementById('counterStart').value = ao._counterStart || 1;
          document.getElementById('counterPadding').value = ao._counterPadding || 3;

          // Set counter mode radio buttons
          const counterMode = ao._counterMode || 'data';
          const radioButtons = document.querySelectorAll('input[name="counterMode"]');
          radioButtons.forEach(radio => {
            radio.checked = radio.value === counterMode;
          });
        } else {
          counterCtrl.style.display = 'none';
        }
        // Text controls visibility (only when a text object is selected)
        const textCtrl = document.getElementById('textControls');
        if (ao && String(ao.type || '').includes('text')) {
          textCtrl.style.display = '';
          // Reflect current formatting into toolbar
          const ff = (ao.fontFamily || '').toLowerCase();
          const currentFamily = ff.includes('sarabun')
            ? 'Sarabun'
            : ff.includes('noto sans thai')
            ? 'Noto Sans Thai'
            : 'system';
          document.getElementById('fontFamily').value = currentFamily;
          // family buttons state
          ['ffSarabun', 'ffNoto', 'ffSystem'].forEach((id) => {
            const btn = document.getElementById(id);
            const match =
              (id === 'ffSarabun' && currentFamily === 'Sarabun') ||
              (id === 'ffNoto' && currentFamily === 'Noto Sans Thai') ||
              (id === 'ffSystem' && currentFamily === 'system');
            btn.classList.toggle('bg-slate-900', !!match);
            btn.classList.toggle('text-white', !!match);
          });

          // size and weight
          const fs = Math.max(8, parseInt(ao.fontSize || 24, 10) || 24);
          document.getElementById('fontSize').value = String(fs);
          const fsDisp = document.getElementById('fsDisp');
          if (fsDisp) fsDisp.textContent = String(fs);
          const w = (ao.fontWeight || '600').toString();
          document.getElementById('fontWeight').value = w;
          // bold, italic, underline, strike states
          const isBold = /^(bold|7\d\d|[89]00)$/i.test(w);
          const stBold = document.getElementById('stBold');
          stBold.classList.toggle('bg-slate-900', isBold);
          stBold.classList.toggle('text-white', isBold);
          const isIt = String(ao.fontStyle || 'normal').toLowerCase() === 'italic';
          const stItalic = document.getElementById('stItalic');
          stItalic.classList.toggle('bg-slate-900', isIt);
          stItalic.classList.toggle('text-white', isIt);
          const isU = !!ao.underline;
          const stU = document.getElementById('stUnderline');
          stU.classList.toggle('bg-slate-900', isU);
          stU.classList.toggle('text-white', isU);
          const isS = !!ao.linethrough;
          const stS = document.getElementById('stStrike');
          stS.classList.toggle('bg-slate-900', isS);
          stS.classList.toggle('text-white', isS);

          // alignment
          const curAlign = String(ao.textAlign || 'left').toLowerCase();
          const norm = ['left', 'center', 'right'].includes(curAlign)
            ? curAlign
            : 'left';
          document.getElementById('textAlign').value = norm;
          const aL = document.getElementById('alLeft');
          const aC = document.getElementById('alCenter');
          const aR = document.getElementById('alRight');
          [aL, aC, aR].forEach((b) => {
            b.classList.remove('bg-slate-900', 'text-white');
          });
          if (norm === 'left') aL.classList.add('bg-slate-900', 'text-white');
          else if (norm === 'center') aC.classList.add('bg-slate-900', 'text-white');
          else if (norm === 'right') aR.classList.add('bg-slate-900', 'text-white');
        } else {
          textCtrl.style.display = 'none';
        }
        // Shape controls visibility (only when a shape is selected)
        const shapeCtrl = document.getElementById('shapeControls');
        const isShape =
          ao &&
          ['line', 'rect', 'circle', 'triangle', 'path'].some((t) =>
            String(ao.type || '').includes(t)
          );
        shapeCtrl.style.display = isShape ? '' : 'none';
        if (isShape) {
          updateShapeControlsState();
        }
      }
      stage.on('selection:created', onSelectionChange);
      stage.on('selection:updated', onSelectionChange);
      stage.on('selection:cleared', onSelectionChange);

      // Image controls listeners
      document.getElementById('imgThreshold').addEventListener('input', () => {
        const ao = stage.getActiveObject();
        if (!ao || !ao._bwManaged) return;
        ao._bwThreshold =
          parseInt(document.getElementById('imgThreshold').value, 10) || 180;
        const src = new Image();
        src.crossOrigin = 'anonymous';
        src.src = ao._srcOrig;
        src.onload = () => {
          const mono = toMonochromeCanvas(src, ao._bwThreshold, !!ao._bwDither);
          const props = {
            left: ao.left,
            top: ao.top,
            scaleX: ao.scaleX,
            scaleY: ao.scaleY,
            angle: ao.angle,
            selectable: true,
          };
          const newImg = new fabric.Image(mono, props);
          newImg._srcOrig = ao._srcOrig;
          newImg._bwThreshold = ao._bwThreshold;
          newImg._bwDither = ao._bwDither;
          newImg._bwManaged = true;
          stage.remove(ao);
          stage.add(newImg).setActiveObject(newImg);
          stage.requestRenderAll();
          snapshot();
        };
      });
      document.getElementById('imgDither').addEventListener('change', () => {
        const ao = stage.getActiveObject();
        if (!ao || !ao._bwManaged) return;
        ao._bwDither = !!document.getElementById('imgDither').checked;
        const src = new Image();
        src.crossOrigin = 'anonymous';
        src.src = ao._srcOrig;
        src.onload = () => {
          const mono = toMonochromeCanvas(
            src,
            ao._bwThreshold || 180,
            !!ao._bwDither
          );
          const props = {
            left: ao.left,
            top: ao.top,
            scaleX: ao.scaleX,
            scaleY: ao.scaleY,
            angle: ao.angle,
            selectable: true,
          };
          const newImg = new fabric.Image(mono, props);
          newImg._srcOrig = ao._srcOrig;
          newImg._bwThreshold = ao._bwThreshold;
          newImg._bwDither = ao._bwDither;
          newImg._bwManaged = true;
          stage.remove(ao);
          stage.add(newImg).setActiveObject(newImg);
          stage.requestRenderAll();
          snapshot();
        };
      });

      document.getElementById('del').onclick = () => {
        const obj = stage.getActiveObject();
        if (obj) {
          stage.remove(obj);
          stage.discardActiveObject();
          stage.requestRenderAll();
        }
        updateActionButtons();
        snapshot();
      };
      document.getElementById('copy').onclick = () => {
        const obj = stage.getActiveObject();
        if (!obj) return;
        obj.clone((cloned) => {
          // place to the right with a small margin; wrap if off-canvas
          const margin = 10;
          const newLeft = Math.min(
            WIDTH_DOTS - cloned.getScaledWidth() - margin,
            obj.left + obj.getScaledWidth() + margin
          );
          cloned.set({
            left: Math.max(5, newLeft),
            top: obj.top,
            evented: true,
          });
          // Preserve B/W image settings on clone
          if (obj._bwManaged) {
            cloned._bwManaged = true;
            cloned._srcOrig = obj._srcOrig;
            cloned._bwThreshold = obj._bwThreshold;
            cloned._bwDither = obj._bwDither;
          }
          stage.add(cloned);
          stage.setActiveObject(cloned);
          stage.requestRenderAll();
          // Refresh controls panel state
          if (typeof onSelectionChange === 'function') onSelectionChange();
          else updateActionButtons();
          snapshot();
        });
      };
      function updateTextSettings() {
        const obj = stage.getActiveObject();
        if (obj && obj.type && obj.type.indexOf('text') !== -1) {
          const familySel = document.getElementById('fontFamily').value;
          const family =
            familySel === 'system'
              ? 'sans-serif'
              : `${familySel}, Noto Sans Thai, sans-serif`;
          const size = Math.max(
            8,
            parseInt(document.getElementById('fontSize').value, 10) || 24
          );
          const weight = Math.max(
            100,
            Math.min(
              900,
              parseInt(document.getElementById('fontWeight').value, 10) || 600
            )
          );
          const align = (
            document.getElementById('textAlign')?.value || 'left'
          ).toLowerCase();
          obj.set({
            fontFamily: family,
            fontSize: size,
            fontWeight: String(weight),
            textAlign: ['left', 'center', 'right'].includes(align)
              ? align
              : 'left',
          });
          stage.requestRenderAll();
          snapshot();
        }
      }
      // Layering (z-index) controls
      document.getElementById('bringFront').onclick = () => {
        const obj = stage.getActiveObject();
        if (!obj) return;
        obj.bringToFront();
        stage.requestRenderAll();
        snapshot();
      };
      document.getElementById('sendBack').onclick = () => {
        const obj = stage.getActiveObject();
        if (!obj) return;
        obj.sendToBack();
        stage.requestRenderAll();
        snapshot();
      };
      // Toolbar behavior: apply instantly to selected text
      (function initTextToolbar() {
        const applyObj = (props = {}) => {
          const obj = stage.getActiveObject();
          if (!obj || !(obj.type || '').includes('text')) return;
          obj.set(props);
          stage.requestRenderAll();
          snapshot();
        };
        const setFamily = (val) => {
          document.getElementById('fontFamily').value = val;
          const family = val === 'system' ? 'sans-serif' : `${val}, Noto Sans Thai, sans-serif`;
          applyObj({ fontFamily: family });
          // visual state
          ['ffSarabun', 'ffNoto', 'ffSystem'].forEach((id) => {
            const btn = document.getElementById(id);
            const match =
              (id === 'ffSarabun' && val === 'Sarabun') ||
              (id === 'ffNoto' && val === 'Noto Sans Thai') ||
              (id === 'ffSystem' && val === 'system');
            btn.classList.toggle('bg-slate-900', !!match);
            btn.classList.toggle('text-white', !!match);
          });
        };
        document.getElementById('ffSarabun').onclick = () => setFamily('Sarabun');
        document.getElementById('ffNoto').onclick = () => setFamily('Noto Sans Thai');
        document.getElementById('ffSystem').onclick = () => setFamily('system');

        const clamp = (v, lo, hi) => Math.max(lo, Math.min(hi, v));
        const setSize = (delta) => {
          const cur = parseInt(document.getElementById('fontSize').value, 10) || 24;
          const next = clamp(cur + delta, 8, 72);
          document.getElementById('fontSize').value = String(next);
          const disp = document.getElementById('fsDisp');
          if (disp) disp.textContent = String(next);
          applyObj({ fontSize: next });
        };
        document.getElementById('fsDec').onclick = () => setSize(-1);
        document.getElementById('fsInc').onclick = () => setSize(+1);

        const toggleBold = () => {
          const cur = (document.getElementById('fontWeight').value || '600').toString();
          const isBold = /^(bold|7\d\d|[89]00)$/i.test(cur);
          const next = isBold ? 400 : 700;
          document.getElementById('fontWeight').value = String(next);
          const b = document.getElementById('stBold');
          b.classList.toggle('bg-slate-900', !isBold);
          b.classList.toggle('text-white', !isBold);
          applyObj({ fontWeight: String(next) });
        };
        document.getElementById('stBold').onclick = toggleBold;

        const toggleItalic = () => {
          const st = document.getElementById('stItalic');
          const active = st.classList.toggle('bg-slate-900');
          st.classList.toggle('text-white', active);
          applyObj({ fontStyle: active ? 'italic' : 'normal' });
        };
        document.getElementById('stItalic').onclick = toggleItalic;

        const toggleUnderline = () => {
          const el = document.getElementById('stUnderline');
          const active = el.classList.toggle('bg-slate-900');
          el.classList.toggle('text-white', active);
          applyObj({ underline: active });
        };
        document.getElementById('stUnderline').onclick = toggleUnderline;

        const toggleStrike = () => {
          const el = document.getElementById('stStrike');
          const active = el.classList.toggle('bg-slate-900');
          el.classList.toggle('text-white', active);
          applyObj({ linethrough: active });
        };
        document.getElementById('stStrike').onclick = toggleStrike;

        const setAlign = (val) => {
          document.getElementById('textAlign').value = val;
          ['alLeft', 'alCenter', 'alRight'].forEach((id) => {
            const b = document.getElementById(id);
            b.classList.remove('bg-slate-900', 'text-white');
          });
          if (val === 'left') document.getElementById('alLeft').classList.add('bg-slate-900', 'text-white');
          if (val === 'center') document.getElementById('alCenter').classList.add('bg-slate-900', 'text-white');
          if (val === 'right') document.getElementById('alRight').classList.add('bg-slate-900', 'text-white');
          applyObj({ textAlign: val });
        };
        document.getElementById('alLeft').onclick = () => setAlign('left');
        document.getElementById('alCenter').onclick = () => setAlign('center');
        document.getElementById('alRight').onclick = () => setAlign('right');
      })();
      // Shape styling functions
      function applyShapeStyle(properties) {
        const obj = stage.getActiveObject();
        if (!obj) return;
        if (
          ['line', 'rect', 'circle', 'triangle', 'path'].some((t) =>
            (obj.type || '').includes(t)
          )
        ) {
          obj.set(properties);
          stage.requestRenderAll();
          snapshot();
          updateShapeControlsState();
        }
      }

      function setLineStyle(style) {
        const dashArrays = {
          'solid': null,
          'dashed': [8, 4],
          'dotted': [2, 4],
          'dashDot': [12, 4, 2, 4]
        };
        applyShapeStyle({ strokeDashArray: dashArrays[style] });
      }

      function setLineThickness(thickness) {
        applyShapeStyle({ strokeWidth: thickness, strokeUniform: true });
      }

      function setFillStyle(fillType) {
        if (fillType === 'none') {
          applyShapeStyle({ fill: 'rgba(0,0,0,0)' });
        } else if (fillType === 'solid') {
          applyShapeStyle({ fill: '#000000' });
        }
      }

      function updateShapeControlsState() {
        const obj = stage.getActiveObject();
        if (!obj) return;

        // Update line style buttons
        const strokeDashArray = obj.strokeDashArray;
        document.querySelectorAll('[id^="lineStyle"]').forEach(btn => btn.classList.remove('bg-blue-500', 'text-white'));

        if (!strokeDashArray) {
          document.getElementById('lineStyleSolid').classList.add('bg-blue-500', 'text-white');
        } else if (JSON.stringify(strokeDashArray) === JSON.stringify([8, 4])) {
          document.getElementById('lineStyleDashed').classList.add('bg-blue-500', 'text-white');
        } else if (JSON.stringify(strokeDashArray) === JSON.stringify([2, 4])) {
          document.getElementById('lineStyleDotted').classList.add('bg-blue-500', 'text-white');
        } else if (JSON.stringify(strokeDashArray) === JSON.stringify([12, 4, 2, 4])) {
          document.getElementById('lineStyleDashDot').classList.add('bg-blue-500', 'text-white');
        }

        // Update thickness buttons
        const strokeWidth = obj.strokeWidth || 2;
        document.querySelectorAll('[id^="thickness"]').forEach(btn => btn.classList.remove('bg-blue-500', 'text-white'));
        const thicknessBtn = document.getElementById(`thickness${Math.round(strokeWidth)}`);
        if (thicknessBtn) {
          thicknessBtn.classList.add('bg-blue-500', 'text-white');
        }

        // Update fill buttons
        const fill = obj.fill;
        document.querySelectorAll('[id^="fill"]').forEach(btn => btn.classList.remove('bg-blue-500', 'text-white'));
        if (!fill || fill === 'rgba(0,0,0,0)' || fill === 'transparent') {
          document.getElementById('fillNone').classList.add('bg-blue-500', 'text-white');
        } else {
          document.getElementById('fillSolid').classList.add('bg-blue-500', 'text-white');
        }
      }

      // Event listeners for shape controls
      document.getElementById('lineStyleSolid').onclick = () => setLineStyle('solid');
      document.getElementById('lineStyleDashed').onclick = () => setLineStyle('dashed');
      document.getElementById('lineStyleDotted').onclick = () => setLineStyle('dotted');
      document.getElementById('lineStyleDashDot').onclick = () => setLineStyle('dashDot');

      document.getElementById('thickness1').onclick = () => setLineThickness(1);
      document.getElementById('thickness2').onclick = () => setLineThickness(2);
      document.getElementById('thickness3').onclick = () => setLineThickness(3);
      document.getElementById('thickness4').onclick = () => setLineThickness(4);
      document.getElementById('thickness5').onclick = () => setLineThickness(5);

      document.getElementById('fillNone').onclick = () => setFillStyle('none');
      document.getElementById('fillSolid').onclick = () => setFillStyle('solid');
      // Keyboard shortcuts: Delete/Backspace/Enter (delete), ESC (deselect)
      window.addEventListener('keydown', (ev) => {
        const key = ev.key;

        // ESC key: deselect all objects
        if (key === 'Escape') {
          stage.discardActiveObject();
          stage.requestRenderAll();
          updateActionButtons();
          ev.preventDefault();
          return;
        }

        // Delete/Backspace/Enter: delete selected object (when not editing text)
        if (!['Delete', 'Backspace', 'Enter'].includes(key)) return;
        const ao = stage.getActiveObject();
        if (!ao) return;
        if (ao.type && ao.type.indexOf('text') !== -1 && ao.isEditing) return;
        ev.preventDefault();
        const obj = ao;
        stage.remove(obj);
        stage.discardActiveObject();
        stage.requestRenderAll();
        updateActionButtons();
        snapshot();
      });

      async function print() {
        try {
          document.getElementById('status').textContent = 'Preparing...';
          const dSel = parseInt(document.getElementById('direction').value, 10);
          let direction = Number.isNaN(dSel) ? 1 : dSel;
          const copies = Math.max(
            1,
            parseInt(document.getElementById('copies').value, 10) || 1
          );
          let printer = document.getElementById('printer').value.trim();

          // If no printer selected, try to auto-select one
          if (!printer) {
            try {
              const res = await fetch('http://localhost:7788/printers/status');
              const data = await res.json();
              if (data.printers && data.printers.length > 0) {
                const preferred =
                  data.printers.find((p) => p.name === 'AiYin_A70Pro') ||
                  data.printers.find((p) => /aiyin|a70/i.test(p.name)) ||
                  data.printers.find((p) => p.isDefault) ||
                  data.printers[0];
                if (preferred) {
                  printer = preferred.name;
                  document.getElementById('printer').value = printer;
                  document.getElementById('status').textContent = `Auto-selected printer: ${printer}`;
                  await new Promise(resolve => setTimeout(resolve, 1000)); // Show message briefly
                }
              }
            } catch (e) {
              console.warn('Failed to auto-select printer:', e);
            }
          }

          // Check if we still don't have a printer
          if (!printer) {
            document.getElementById('status').textContent = 'Please select a printer first (click Refresh button)';
            return;
          }

          const collation =
            document.getElementById('collation')?.value || 'collated';
          const reverse = !!document.getElementById('reverseOrder')?.checked;

        // Try to parse data array if provided
        let dataArr = [];
        try {
          const raw = (document.getElementById('dataJSON').value || '').trim();
          if (raw) {
            const parsed = JSON.parse(raw);
            if (Array.isArray(parsed)) dataArr = parsed;
          }
        } catch (_) {}

        // Render to an offscreen canvas to avoid selection boxes
        stage.discardActiveObject();
        stage.requestRenderAll();
        const canvasEl = document.createElement('canvas');
        canvasEl.width = WIDTH_DOTS;
        canvasEl.height = HEIGHT_DOTS;
        const ctx = canvasEl.getContext('2d');
        ctx.fillStyle = '#fff';
        ctx.fillRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
        // draw using built-in fabric render to dataURL; then draw image back
        const dataURL = stage.toDataURL({
          format: 'png',
          multiplier: 1,
          enableRetinaScaling: false,
        });
        await new Promise((resolve) => {
          const img = new Image();
          img.onload = () => {
            ctx.drawImage(img, 0, 0);
            resolve();
          };
          img.src = dataURL;
        });

        // Preserve WYSIWYG: if direction is 1 (flip 180 on printer), rotate bitmap 180 and send direction 0
        let srcCanvas = canvasEl;
        if (direction === 1) {
          const rot = document.createElement('canvas');
          rot.width = WIDTH_DOTS;
          rot.height = HEIGHT_DOTS;
          const rctx = rot.getContext('2d');
          rctx.translate(WIDTH_DOTS, HEIGHT_DOTS);
          rctx.rotate(Math.PI);
          rctx.drawImage(canvasEl, 0, 0);
          srcCanvas = rot;
          direction = 0;
        }

        async function sendBatch(items) {
          const payload = {
            printer: printer || undefined,
            items,
            sizeMm: { w: CURRENT_WIDTH_MM, h: CURRENT_HEIGHT_MM },
            gapMm: { h: 2, off: 0 },
            direction,
            speed: 4,
            density: 8,
            copies,
            invert: true,
          };
          const res = await fetch('http://localhost:7788/print/bitmap-batch', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
          });
          const data = await res.json();
          if (!res.ok) throw new Error(data.error || 'Print failed');
          return data;
        }

        // Helper: apply bindings for a record
        async function applyBindingsFor(record, recordIndex = 0, printIndex = 0) {
          // For text/barcode/qr; allow awaiting within loop
          const objs = stage.getObjects();
          for (const obj of objs) {
            // Handle special fields (date, time, counter)
            if (obj._isDateField) {
              const today = new Date();
              const dateStr = today.toLocaleDateString('th-TH', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              });
              obj.text = dateStr;
            } else if (obj._isTimeField) {
              const now = new Date();
              const timeStr = now.toLocaleTimeString('th-TH', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              });
              obj.text = timeStr;
            } else if (obj._isCounterField) {
              const counterMode = obj._counterMode || 'data';
              let counterValue;

              if (counterMode === 'print') {
                // Count by print sequence (total print index)
                counterValue = (obj._counterStart || 1) + printIndex;
              } else {
                // Count by data records (default)
                counterValue = (obj._counterStart || 1) + recordIndex;
              }

              const padding = obj._counterPadding || 3;
              obj.text = counterValue.toString().padStart(padding, '0');
            } else if (
              obj._bindingKey &&
              typeof obj._bindingKey === 'string' &&
              obj._bindingKey
            ) {
              const val =
                record &&
                Object.prototype.hasOwnProperty.call(record, obj._bindingKey)
                  ? record[obj._bindingKey]
                  : '';
              if (obj.type && String(obj.type).includes('text')) {
                if (!obj._templateText && typeof obj.text === 'string')
                  obj._templateText = obj.text;
                obj.text = String(val ?? '');
              } else if (obj._kind === 'barcode') {
                updateBarcodeObject(obj, String(val ?? ''));
              } else if (obj._kind === 'qr') {
                await ensureQRCode();
                updateQRObject(obj, String(val ?? ''));
              }
            }
          }
          stage.requestRenderAll();
          await new Promise((r) => setTimeout(r, 10));
        }

        document.getElementById('status').textContent = 'Printing…';
        if (dataArr.length > 0) {
            const records = reverse
              ? dataArr.slice().reverse()
              : dataArr.slice();
            // Pre-render each record to bytes and send one batch job for smooth printing
            const items = [];
            let printIndex = 0; // Track total print sequence

            // Check if any counter uses "print" mode
            const hasSequentialCounter = stage.getObjects().some(obj =>
              obj._isCounterField && obj._counterMode === 'print'
            );

            if (hasSequentialCounter) {
              // For sequential counters: render each label individually regardless of collation
              for (let i = 0; i < records.length; i++) {
                const rec = records[i];
                for (let c = 0; c < copies; c++) {
                  await applyBindingsFor(rec, i, printIndex);
                  const snap = stage.toDataURL({
                    format: 'png',
                    multiplier: 1,
                    enableRetinaScaling: false,
                  });
                  await new Promise((resolve) => {
                    const img2 = new Image();
                    img2.onload = () => {
                      ctx.clearRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
                      ctx.fillStyle = '#fff';
                      ctx.fillRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
                      ctx.drawImage(img2, 0, 0);
                      resolve();
                    };
                    img2.src = snap;
                  });
                  const printCanvas = createPrintCanvasFromStageCanvas(canvasEl, direction);
                  const bytes = pack1bppFromCanvas(printCanvas, 200);
                  const bytesBase64 = btoa(String.fromCharCode(...bytes));

                  items.push({
                    widthDots: mmToDots(CURRENT_WIDTH_MM),
                    heightDots: mmToDots(CURRENT_HEIGHT_MM),
                    bytesBase64,
                    xDots: 0,
                    yDots: 0,
                  });
                  printIndex++; // Increment for each individual print
                }
                document.getElementById('status').textContent = `Prepared ${printIndex}/${records.length * copies}`;
              }
            } else {
              // Original logic for data-based counters
              for (let i = 0; i < records.length; i++) {
                const rec = records[i];

                if (collation === 'uncollated') {
                  // For uncollated: render once, then repeat for each copy
                  await applyBindingsFor(rec, i, printIndex);
                  const snap = stage.toDataURL({
                    format: 'png',
                    multiplier: 1,
                    enableRetinaScaling: false,
                  });
                  await new Promise((resolve) => {
                    const img2 = new Image();
                    img2.onload = () => {
                      ctx.clearRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
                      ctx.fillStyle = '#fff';
                      ctx.fillRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
                      ctx.drawImage(img2, 0, 0);
                      resolve();
                    };
                    img2.src = snap;
                  });
                  const printCanvas = createPrintCanvasFromStageCanvas(canvasEl, direction);
                  const bytes = pack1bppFromCanvas(printCanvas, 200);
                  const bytesBase64 = btoa(String.fromCharCode(...bytes));

                  for (let c = 0; c < copies; c++) {
                    items.push({
                      widthDots: mmToDots(CURRENT_WIDTH_MM),
                      heightDots: mmToDots(CURRENT_HEIGHT_MM),
                      bytesBase64,
                      xDots: 0,
                      yDots: 0,
                    });
                  }
                } else {
                  // For collated: render once per record
                  await applyBindingsFor(rec, i, printIndex);
                  const snap = stage.toDataURL({
                    format: 'png',
                    multiplier: 1,
                    enableRetinaScaling: false,
                  });
                  await new Promise((resolve) => {
                    const img2 = new Image();
                    img2.onload = () => {
                      ctx.clearRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
                      ctx.fillStyle = '#fff';
                      ctx.fillRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
                      ctx.drawImage(img2, 0, 0);
                      resolve();
                    };
                    img2.src = snap;
                  });
                  const printCanvas = createPrintCanvasFromStageCanvas(canvasEl, direction);
                  const bytes = pack1bppFromCanvas(printCanvas, 200);
                  const bytesBase64 = btoa(String.fromCharCode(...bytes));

                  items.push({
                    widthDots: mmToDots(CURRENT_WIDTH_MM),
                    heightDots: mmToDots(CURRENT_HEIGHT_MM),
                    bytesBase64,
                    xDots: 0,
                    yDots: 0,
                  });
                }
                document.getElementById('status').textContent = `Prepared ${i + 1}/${records.length}`;
              }
            }
            // For uncollated, we already expanded copies into items list; send with copies=1
            const savedCopies = copies;
            const prevCopiesField = document.getElementById('copies');
            if (collation === 'uncollated') {
              // temporarily tell sendBatch to send copies=1 by passing a local value
            }
            await (async () => {
              // sendBatch reads from outer scope 'copies', so we inline a small override
              const payload = {
                printer: printer || undefined,
                items,
                sizeMm: { w: CURRENT_WIDTH_MM, h: CURRENT_HEIGHT_MM },
                gapMm: { h: 2, off: 0 },
                direction,
                speed: 4,
                density: 8,
                copies: collation === 'uncollated' ? 1 : savedCopies,
                invert: true,
              };
              const res = await fetch(
                'http://localhost:7788/print/bitmap-batch',
                {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(payload),
                }
              );
              const data = await res.json();
              if (!res.ok) throw new Error(data.error || 'Print failed');
            })();
            document.getElementById(
              'status'
            ).textContent = `Done (${records.length} records × ${savedCopies} copies)`;
          } else {
            const printCanvas = createPrintCanvas();
            const bytes = pack1bppFromCanvas(printCanvas, 200);
            const bytesBase64 = btoa(String.fromCharCode(...bytes));
            await sendBatch([
              {
                widthDots: mmToDots(CURRENT_WIDTH_MM),
                heightDots: mmToDots(CURRENT_HEIGHT_MM),
                bytesBase64,
                xDots: 0,
                yDots: 0,
              },
            ]);
            document.getElementById('status').textContent = 'Done';
          }
        } catch (e) {
          document.getElementById('status').textContent =
            e.message || String(e);
        }
      }

      function createTemplatePreview() {
        if (CURRENT_ORIENTATION === 'landscape') {
          // For landscape, create a preview that shows the correct aspect ratio
          // Create a temporary canvas with actual paper dimensions
          const actualWidth = mmToDots(CURRENT_WIDTH_MM);
          const actualHeight = mmToDots(CURRENT_HEIGHT_MM);

          const previewCanvas = document.createElement('canvas');
          previewCanvas.width = actualWidth;
          previewCanvas.height = actualHeight;

          const previewCtx = previewCanvas.getContext('2d');
          previewCtx.fillStyle = 'white';
          previewCtx.fillRect(0, 0, actualWidth, actualHeight);

          // Get the current stage canvas
          const stageCanvas = stage.getElement();

          // Rotate and draw the stage content to match actual paper orientation
          previewCtx.translate(actualWidth / 2, actualHeight / 2);
          previewCtx.rotate(Math.PI / 2); // 90 degrees clockwise
          previewCtx.translate(-stageCanvas.width / 2, -stageCanvas.height / 2);
          previewCtx.drawImage(stageCanvas, 0, 0);

          return previewCanvas.toDataURL({ format: 'png' });
        } else {
          // For portrait, use the normal stage preview
          return stage.toDataURL({ format: 'png' });
        }
      }

      // Templates (IndexedDB)
      const DB_NAME = 'A70ProTemplates';
      const DB_VERSION = 1;
      const STORE_NAME = 'templates';

      let db = null;

      // Initialize IndexedDB
      function initDB() {
        return new Promise((resolve, reject) => {
          const request = indexedDB.open(DB_NAME, DB_VERSION);

          request.onerror = () => reject(request.error);
          request.onsuccess = () => {
            db = request.result;
            resolve(db);
          };

          request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains(STORE_NAME)) {
              const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
              store.createIndex('createdAt', 'createdAt', { unique: false });
            }
          };
        });
      }

      async function loadTemplates() {
        let arr = [];
        try {
          if (!db) await initDB();

          const transaction = db.transaction([STORE_NAME], 'readonly');
          const store = transaction.objectStore(STORE_NAME);
          const index = store.index('createdAt');
          const request = index.getAll();

          arr = await new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => reject(request.error);
          });

          // Sort by creation date (newest first)
          arr.sort((a, b) => b.createdAt - a.createdAt);

        } catch (error) {
          console.error('Error loading templates:', error);
          const cont = document.getElementById('templates');
          cont.innerHTML = '<div class="text-sm text-red-500">Error loading templates</div>';
          return;
        }

        const cont = document.getElementById('templates');
        cont.innerHTML = '';
        if (!arr.length) {
          const empty = document.createElement('div');
          empty.className = 'text-sm text-slate-500';
          empty.textContent = 'No saved templates. Click Save to add one.';
          cont.append(empty);
          return;
        }

        const dir =
          parseInt(document.getElementById('direction').value, 10) || 1;
        arr.forEach((t) => {
          const card = document.createElement('div');
          card.className = 'border rounded p-2 bg-white';
          const title = document.createElement('div');
          title.className = 'text-xs text-slate-500';
          title.textContent = new Date(t.createdAt).toLocaleString();
          const name = document.createElement('div');
          name.className = 'text-sm font-medium truncate';
          name.title = t.name || 'Template';
          name.textContent = t.name || 'Template';

          // Show paper size and orientation info
          const sizeInfo = document.createElement('div');
          sizeInfo.className = 'text-xs text-blue-600 mt-1';

          let sizeText = '';
          if (t.widthMm && t.heightMm) {
            sizeText = `${t.widthMm}×${t.heightMm} mm`;
          } else if (t.paperSize && PAPER_SIZES[t.paperSize]) {
            const size = PAPER_SIZES[t.paperSize];
            sizeText = `${size.width}×${size.height} mm`;
          } else {
            sizeText = '60×20 mm'; // fallback
          }

          // Add orientation info
          const orientation = t.orientation || 'portrait';
          const orientationIcon = orientation === 'landscape' ? '🔄' : '📄';
          const orientationText = orientation === 'landscape' ? 'Landscape' : 'Portrait';

          sizeInfo.textContent = `${orientationIcon} ${sizeText} (${orientationText})`;

          const img = document.createElement('img');
          img.src = t.preview || '';
          img.className = 'mt-1 border rounded';

          // Calculate aspect ratio for proper image display
          const templateWidth = t.widthMm || (t.paperSize && PAPER_SIZES[t.paperSize] ? PAPER_SIZES[t.paperSize].width : 60);
          const templateHeight = t.heightMm || (t.paperSize && PAPER_SIZES[t.paperSize] ? PAPER_SIZES[t.paperSize].height : 20);
          const aspectRatio = templateWidth / templateHeight;

          // Set image size based on aspect ratio (max width 120px)
          const maxWidth = 120;
          const maxHeight = 80;
          if (aspectRatio > maxWidth / maxHeight) {
            img.style.width = maxWidth + 'px';
            img.style.height = (maxWidth / aspectRatio) + 'px';
          } else {
            img.style.height = maxHeight + 'px';
            img.style.width = (maxHeight * aspectRatio) + 'px';
          }

          if (dir === 0) img.style.transform = 'rotate(180deg)';
          const actions = document.createElement('div');
          actions.className = 'mt-2 flex gap-2';
          const btnLoad = document.createElement('button');
          btnLoad.className =
            'text-xs px-2 py-1 bg-slate-200 rounded hover:bg-slate-300';
          btnLoad.textContent = 'Load';
          btnLoad.onclick = () => loadTemplate(t);
          // const btnPrint = document.createElement('button'); btnPrint.className='text-xs px-2 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-500'; btnPrint.textContent='Print'; btnPrint.onclick= async () => { await loadTemplate(t, true); await print(); };
          const btnDel = document.createElement('button');
          btnDel.className =
            'text-xs px-2 py-1 bg-rose-600 text-white rounded hover:bg-rose-500';
          btnDel.textContent = 'Delete';
          btnDel.onclick = () => deleteTemplate(t);
          // actions.append(btnLoad, btnPrint, btnDel);
          actions.append(btnLoad, btnDel);
          card.append(title, name, sizeInfo, img, actions);
          cont.append(card);
        });
      }
      async function saveTemplate() {
        try {
          if (!db) await initDB();

          const name = 'Label ' + new Date().toLocaleString();
          const preview = createTemplatePreview();
          const paperSize = document.getElementById('paperSize').value;
          const rec = {
            id: Date.now().toString(),
            name,
            createdAt: Date.now(),
            preview,
            fabric: stageJSON(),
            paperSize: paperSize,
            customWidth: paperSize === 'custom' ? CURRENT_WIDTH_MM : null,
            customHeight: paperSize === 'custom' ? CURRENT_HEIGHT_MM : null,
            widthMm: CURRENT_WIDTH_MM,
            heightMm: CURRENT_HEIGHT_MM,
            orientation: CURRENT_ORIENTATION
          };

          const transaction = db.transaction([STORE_NAME], 'readwrite');
          const store = transaction.objectStore(STORE_NAME);

          await new Promise((resolve, reject) => {
            const request = store.add(rec);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
          });

          // Clean up old templates (keep only latest 100)
          await cleanupOldTemplates();

          await loadTemplates();
          document.getElementById('status').textContent = 'Template saved';
        } catch (error) {
          console.error('Error saving template:', error);
          document.getElementById('status').textContent = 'Error saving template';
        }
      }
      async function deleteTemplate(t) {
        try {
          if (!db) await initDB();

          const transaction = db.transaction([STORE_NAME], 'readwrite');
          const store = transaction.objectStore(STORE_NAME);

          await new Promise((resolve, reject) => {
            const request = store.delete(t.id);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
          });

          await loadTemplates();
          document.getElementById('status').textContent = 'Template deleted';
        } catch (error) {
          console.error('Error deleting template:', error);
          document.getElementById('status').textContent = 'Error deleting template';
        }
      }

      async function cleanupOldTemplates() {
        try {
          const transaction = db.transaction([STORE_NAME], 'readwrite');
          const store = transaction.objectStore(STORE_NAME);
          const index = store.index('createdAt');
          const request = index.getAll();

          const templates = await new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = () => reject(request.error);
          });

          // Sort by creation date (newest first) and keep only latest 100
          templates.sort((a, b) => b.createdAt - a.createdAt);
          const templatesToDelete = templates.slice(100); // Remove templates beyond 100

          // Delete old templates
          for (const template of templatesToDelete) {
            await new Promise((resolve, reject) => {
              const deleteRequest = store.delete(template.id);
              deleteRequest.onsuccess = () => resolve();
              deleteRequest.onerror = () => reject(deleteRequest.error);
            });
          }
        } catch (error) {
          console.error('Error cleaning up old templates:', error);
        }
      }

      function loadTemplate(t, silent) {
        try {
          // Load paper size first - always use template's dimensions
          const paperSizeSelect = document.getElementById('paperSize');
          const customInputs = document.getElementById('customSizeInputs');

          // Get template dimensions (prioritize widthMm/heightMm over paperSize)
          let templateWidth = t.widthMm;
          let templateHeight = t.heightMm;

          // If no direct dimensions, try to get from paperSize
          if (!templateWidth || !templateHeight) {
            if (t.paperSize && PAPER_SIZES[t.paperSize]) {
              const size = PAPER_SIZES[t.paperSize];
              templateWidth = size.width;
              templateHeight = size.height;
            } else if (t.customWidth && t.customHeight) {
              templateWidth = t.customWidth;
              templateHeight = t.customHeight;
            } else {
              // Ultimate fallback
              templateWidth = 60;
              templateHeight = 20;
            }
          }

          // Check if template size matches any standard size
          let matchedStandardSize = null;
          for (const [sizeKey, sizeValue] of Object.entries(PAPER_SIZES)) {
            if (sizeValue.width === templateWidth && sizeValue.height === templateHeight) {
              matchedStandardSize = sizeKey;
              break;
            }
          }

          if (matchedStandardSize) {
            // Use standard size
            paperSizeSelect.value = matchedStandardSize;
            customInputs.classList.add('hidden');
            updateCanvasSize(templateWidth, templateHeight);
          } else {
            // Use custom size
            paperSizeSelect.value = 'custom';
            customInputs.classList.remove('hidden');
            document.getElementById('customWidth').value = templateWidth;
            document.getElementById('customHeight').value = templateHeight;
            updateCanvasSize(templateWidth, templateHeight);
          }

          // Load orientation
          const templateOrientation = t.orientation || 'portrait';
          setOrientation(templateOrientation);

          // Then load fabric content
          stage.loadFromJSON(t.fabric, () => {
            stage.renderAll();
            // After loading a template, update Print button state based on object count and printer status
            updateActionButtons();
            renderBindingsList();
            if (!silent)
              document.getElementById('status').textContent = 'Template loaded';
          });
        } catch (e) {
          document.getElementById('status').textContent =
            'Failed to load template';
        }
      }

      document.getElementById('saveTpl').onclick = () => saveTemplate().catch(console.error);
      document.getElementById('print').onclick = print;
      // Preview button: apply to all bound elements using selected row
      const rerenderBindingsOnDataChange = () => {
        try {
          renderBindingsList();
        } catch (_) {}
      };
      document
        .getElementById('dataJSON')
        .addEventListener('input', rerenderBindingsOnDataChange);
      document
        .getElementById('dataJSON')
        .addEventListener('change', rerenderBindingsOnDataChange);
      document.getElementById('previewData').onclick = async () => {
        const raw = (document.getElementById('dataJSON').value || '').trim();
        if (!raw) {
          document.getElementById('dataStatus').textContent =
            'Paste JSON array first';
          return;
        }
        try {
          const arr = JSON.parse(raw);
          if (!Array.isArray(arr) || arr.length === 0) {
            document.getElementById('dataStatus').textContent =
              'Expected a non-empty array';
            return;
          }
          const idx = Math.max(
            1,
            parseInt(document.getElementById('dataIndex').value, 10) || 1
          );
          const rec = arr[idx - 1] || arr[0];
          // Apply bindings
          for (const obj of stage.getObjects()) {
            if (obj._bindingKey) {
              const val = rec[obj._bindingKey];
              if (obj.type && String(obj.type).includes('text')) {
                if (!obj._templateText && typeof obj.text === 'string')
                  obj._templateText = obj.text;
                obj.text = String(val ?? '');
              } else if (obj._kind === 'barcode') {
                updateBarcodeObject(obj, String(val ?? ''));
              } else if (obj._kind === 'qr') {
                await ensureQRCode();
                updateQRObject(obj, String(val ?? ''));
              }
            }
          }
          stage.requestRenderAll();
          document.getElementById(
            'dataStatus'
          ).textContent = `Preview row ${idx}/${arr.length}`;
        } catch (e) {
          document.getElementById('dataStatus').textContent =
            'Invalid JSON array';
        }
      };
      document.getElementById('undoBtn').onclick = undo;
      // Keyboard: delete/backspace/enter already handled; add Ctrl/Cmd+Z for undo
      window.addEventListener('keydown', (ev) => {
        const z = ev.key === 'z' || ev.key === 'Z';
        if ((ev.ctrlKey || ev.metaKey) && z) {
          ev.preventDefault();
          undo();
        }
      });

      // Counter settings auto-apply event listeners
      document.getElementById('counterStart').addEventListener('input', updateCounterSettings);
      document.getElementById('counterPadding').addEventListener('change', updateCounterSettings);
      document.querySelectorAll('input[name="counterMode"]').forEach(radio => {
        radio.addEventListener('change', updateCounterSettings);
      });

      // Text settings auto-apply event listeners
      document.getElementById('fontFamily').addEventListener('change', updateTextSettings);
      document.getElementById('fontSize').addEventListener('input', updateTextSettings);
      document.getElementById('fontWeight').addEventListener('input', updateTextSettings);
      document.getElementById('textAlign').addEventListener('change', updateTextSettings);

      // initialize history
      snapshot();
      loadTemplates().catch(console.error);
      updateActionButtons();

      // Attempt to auto-load default data.json if textarea empty
      async function tryFetchJSON(url) {
        try {
          const res = await fetch(url, { cache: 'no-store' });
          if (!res.ok) return null;
          const data = await res.json();
          return data;
        } catch (_) {
          return null;
        }
      }
      (async () => {
        const ta = document.getElementById('dataJSON');
        // Only try to fetch JSON if we're running on HTTP(S), not file://
        if (ta && !ta.value.trim() && (location.protocol === 'http:' || location.protocol === 'https:')) {
          const candidates = ['data.json', '../data.json', '/data.json'];
          for (const u of candidates) {
            const data = await tryFetchJSON(u);
            if (Array.isArray(data)) {
              ta.value = JSON.stringify(data, null, 2);
              document.getElementById(
                'dataStatus'
              ).textContent = `Loaded ${data.length} records from ${u}`;
              break;
            }
          }
          renderBindingsList();
        } else if (ta && !ta.value.trim()) {
          // Show placeholder data for file:// protocol
          const sampleData = [
            { "uic": "XX010101", "id_card": "1234123412340" },
            { "uic": "YY010101", "id_card": "1234123412341" }
          ];
          ta.value = JSON.stringify(sampleData, null, 2);
          document.getElementById('dataStatus').textContent = 'Sample data loaded (paste your own JSON here)';
          renderBindingsList();
        }
      })();
      renderBindingsList();

      // --- Printers UI ---
      let __printersState = {};
      function updatePrintButtonByState() {
        const btn = document.getElementById('print');
        const sel = (document.getElementById('printer').value || '').trim();
        const state = (window.__printersState || __printersState)[sel];
        const online = state === 'idle' || state === 'printing';
        const hasObjects = stage.getObjects().length > 0;
        const enabled = online && hasObjects;
        btn.disabled = !enabled;
        btn.className = enabled
          ? 'px-5 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 text-base font-semibold shadow inline-flex items-center gap-2'
          : 'px-5 py-3 bg-slate-300 text-slate-500 rounded text-base font-semibold shadow inline-flex items-center gap-2 cursor-not-allowed';
      }
      async function refreshPrinters() {
        const list = document.getElementById('printerList');
        list.innerHTML = '<div class="text-sm text-slate-500">Loading…</div>';
        try {
          const res = await fetch('http://localhost:7788/printers/status');
          const data = await res.json();
          list.innerHTML = '';
          const current = (
            document.getElementById('printer').value || ''
          ).trim();
          window.__printersState = {};
          (data.printers || []).forEach((p) => {
            window.__printersState[p.name] = p.state;
            const row = document.createElement('button');
            row.type = 'button';
            row.className =
              'flex items-center justify-between w-full px-2 py-1 rounded hover:bg-green-300 text-left';
            const left = document.createElement('div');
            left.className = 'flex items-center gap-2';
            let color = 'bg-slate-400';
            if (p.state === 'idle') color = 'bg-green-500';
            else if (p.state === 'printing') color = 'bg-amber-500';
            else if (p.state === 'offline' || p.state === 'disabled')
              color = 'bg-rose-500';
            const dot = document.createElement('span');
            dot.className = 'inline-block w-2 h-2 rounded-full ' + color;
            const name = document.createElement('span');
            name.textContent = p.name + (p.isDefault ? ' (default)' : '');
            left.append(dot, name);
            const right = document.createElement('span');
            right.className = 'text-xs text-slate-500';
            right.textContent = p.state;
            row.append(left, right);
            if (p.name === current) row.classList.add('bg-green-300');
            row.onclick = () => {
              document.getElementById('printer').value = p.name;
              [...list.children].forEach((ch) =>
                ch.classList.remove('bg-green-300')
              );
              row.classList.add('bg-green-300');
              updatePrintButtonByState();
            };
            list.append(row);
          });
          if (!current && data.printers && data.printers.length) {
            const preferred =
              data.printers.find((p) => p.name === 'AiYin_A70Pro') ||
              data.printers.find((p) => /aiyin|a70/i.test(p.name)) ||
              data.printers.find((p) => p.isDefault) ||
              data.printers[0];
            const selName = preferred ? preferred.name : '';
            document.getElementById('printer').value = selName;
            // highlight exact match
            [...list.children].forEach((ch) => {
              if (ch.textContent && ch.textContent.startsWith(selName))
                ch.classList.add('bg-green-300');
            });
          }
          updatePrintButtonByState();
        } catch (e) {
          list.innerHTML =
            '<div class="text-sm text-rose-600">Failed to load printers</div>';
          __printersState = {};
          updatePrintButtonByState();
        }
      }
      document.getElementById('refreshPrinters').onclick = refreshPrinters;
      refreshPrinters();

      // Push snapshots on significant changes
      stage.on('object:modified', snapshot);
      stage.on('object:added', () => {
        snapshot();
        updateActionButtons();
        renderBindingsList();
      });
      stage.on('object:removed', () => {
        snapshot();
        updateActionButtons();
        renderBindingsList();
      });

      // Canvas display size is now handled by FabricJS zoom, no need for after:render scaling

      // Clear canvas action
      document.getElementById('clearCanvas')?.addEventListener('click', () => {
        stage
          .getObjects()
          .slice()
          .forEach((o) => stage.remove(o));
        stage.discardActiveObject();
        stage.requestRenderAll();
        snapshot();
        updateActionButtons();
      });

      // Paper size change handlers
      document.getElementById('paperSize').addEventListener('change', handlePaperSizeChange);
      document.getElementById('applyCustomSize').addEventListener('click', applyCustomSize);

      // Orientation change handlers
      document.getElementById('orientationPortrait').addEventListener('click', () => setOrientation('portrait'));
      document.getElementById('orientationLandscape').addEventListener('click', () => setOrientation('landscape'));

      // Allow Enter key in custom size inputs to apply
      document.getElementById('customWidth').addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          applyCustomSize();
        }
      });
      document.getElementById('customHeight').addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          applyCustomSize();
        }
      });

      // Initialize proper zoom on page load
      document.addEventListener('DOMContentLoaded', () => {
        // Wait for everything to be ready, then apply initial zoom
        setTimeout(() => {
          const MAX_DISPLAY_WIDTH = 500;
          const aspectRatio = HEIGHT_DOTS / WIDTH_DOTS;
          const displayWidth = Math.min(WIDTH_DOTS, MAX_DISPLAY_WIDTH);
          const displayHeight = displayWidth * aspectRatio;
          applyCanvasDisplaySize(displayWidth, displayHeight);
        }, 100);
      });
    </script>
  </body>
</html>
