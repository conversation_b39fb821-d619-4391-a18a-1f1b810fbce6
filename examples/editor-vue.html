<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Label Editor (Vue 3 + Tailwind)</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    html, body { height: 100%; }
    body { font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, 'Noto Sans Thai', sans-serif; }
    canvas { image-rendering: pixelated; }
    /* Ensure CSS pixel size matches canvas intrinsic size to avoid pointer mismatch */
    #stage { width: 480px; height: 160px; touch-action: none; }
  </style>
  <!-- Local libs (place files in examples/lib) -->
  <script src="./lib/fabric.min.js"></script>
  <script src="./lib/qrcode.min.js"></script>
  <!-- Vue 3 from CDN (allowed, small single file) -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
</head>
<body class="bg-slate-50">
  <div id="app" class="max-w-6xl mx-auto p-4">
    <h1 class="text-xl font-semibold mb-4">A70Pro Label Editor (60×20 mm @ 203 dpi)</h1>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="md:col-span-2 space-y-3">
        <div class="flex flex-wrap items-center gap-3">
          <div>
            <label class="block text-xs text-slate-500">Printer</label>
            <input v-model="printer" class="px-2 py-1 border rounded w-56" placeholder="optional CUPS queue" />
          </div>
          <div>
            <label class="block text-xs text-slate-500">Direction</label>
            <select v-model.number="direction" class="px-2 py-1 border rounded">
              <option :value="0">0 (front)</option>
              <option :value="1">1 (flip 180°)</option>
            </select>
          </div>
          <div>
            <label class="block text-xs text-slate-500">Copies</label>
            <input v-model.number="copies" type="number" min="1" class="px-2 py-1 border rounded w-20" />
          </div>
          <div>
            <label class="block text-xs text-slate-500">Left Offset (mm)</label>
            <input v-model.number="leftMm" type="number" step="0.5" class="px-2 py-1 border rounded w-24" />
          </div>
          <div class="mt-6 space-x-2">
            <button @click="newCanvas" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">New</button>
            <button @click="onPrint" class="px-3 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-500">Print</button>
            <span class="ml-2 text-sm text-slate-600">{{ status }}</span>
          </div>
        </div>

        <div class="flex flex-wrap items-center gap-3">
          <div>
            <label class="block text-xs text-slate-500">Font Family</label>
            <select v-model="fontFamily" class="px-2 py-1 border rounded">
              <option value="Sarabun, Noto Sans Thai, sans-serif">Sarabun/Noto/System</option>
              <option value="Noto Sans Thai, sans-serif">Noto Sans Thai</option>
              <option value="sans-serif">System Sans</option>
            </select>
          </div>
          <div>
            <label class="block text-xs text-slate-500">Font Size</label>
            <input v-model.number="fontSize" type="number" min="8" max="72" class="px-2 py-1 border rounded w-24" />
          </div>
          <div>
            <label class="block text-xs text-slate-500">Font Weight</label>
            <input v-model.number="fontWeight" type="number" min="100" max="900" step="50" class="px-2 py-1 border rounded w-28" />
          </div>
          <div>
            <label class="block text-xs text-slate-500">Stroke Width</label>
            <input v-model.number="strokeWidth" type="number" min="0" step="0.5" class="px-2 py-1 border rounded w-24" />
          </div>
          <div class="mt-6 space-x-2">
            <button @click="applyTextToSelection" class="px-3 py-2 bg-slate-700 text-white rounded hover:bg-slate-600">Apply Text</button>
            <button @click="applyStrokeToSelection" class="px-3 py-2 bg-slate-700 text-white rounded hover:bg-slate-600">Apply Stroke</button>
          </div>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <button @click="addText" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Add Text</button>
          <button @click="addBarcode" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Add Barcode</button>
          <button @click="addQR" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Add QR</button>
          <label class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300 cursor-pointer">
            Upload Image
            <input type="file" class="hidden" @change="onImageFile" accept="image/*" />
          </label>
          <div class="h-6 border-l mx-1"></div>
          <button @click="addShape('line')" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Line</button>
          <button @click="addShape('rect')" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Rectangle</button>
          <button @click="addShape('circle')" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Circle</button>
          <button @click="addShape('triangle')" class="px-3 py-2 bg-slate-200 rounded hover:bg-slate-300">Triangle</button>
          <div class="h-6 border-l mx-1"></div>
          <button :disabled="!hasSelection" @click="copySelected" class="px-3 py-2 rounded" :class="hasSelection ? 'bg-emerald-600 text-white hover:bg-emerald-500' : 'bg-slate-200 text-slate-400'">Copy Selected</button>
          <button :disabled="!hasSelection" @click="deleteSelected" class="px-3 py-2 rounded" :class="hasSelection ? 'bg-rose-600 text-white hover:bg-rose-500' : 'bg-slate-200 text-slate-400'">Delete Selected</button>
        </div>

        <div class="inline-block border rounded bg-white mt-2">
          <canvas id="stage" width="480" height="160"></canvas>
        </div>
      </div>

      <div class="space-y-3">
          <div class="flex items-center justify-between">
          <h2 class="font-medium">Templates</h2>
        </div>
        <div v-if="templates.length === 0" class="text-sm text-slate-500">No saved templates yet. Printing will save the current layout.</div>
        <div class="grid grid-cols-2 gap-3">
          <div v-for="t in templates" :key="t.id" class="border rounded p-2 bg-white">
            <div class="text-xs text-slate-500">{{ new Date(t.createdAt).toLocaleString() }}</div>
            <div class="text-sm font-medium truncate" :title="t.name">{{ t.name }}</div>
            <img v-if="t.preview" :src="t.preview" alt="preview" class="mt-1 border rounded"
                 :style="{ transform: direction === 0 ? 'rotate(180deg)' : 'none' }" />
            <div class="mt-2 flex gap-2">
              <button @click="loadTemplate(t)" class="text-xs px-2 py-1 bg-slate-200 rounded hover:bg-slate-300">Load</button>
              <button @click="printTemplate(t)" class="text-xs px-2 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-500">Print</button>
              <button @click="deleteTemplate(t)" class="text-xs px-2 py-1 bg-rose-600 text-white rounded hover:bg-rose-500">Delete</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
  const { createApp, ref, reactive, computed, onMounted } = Vue;

  const DPI = 203, WIDTH_DOTS = 480, HEIGHT_DOTS = 160;

  function pack1bppFromCanvas(canvas, threshold = 200) {
    const w = canvas.width, h = canvas.height;
    const ctx = canvas.getContext('2d');
    const img = ctx.getImageData(0, 0, w, h).data;
    const bytesPerRow = Math.ceil(w / 8);
    const out = new Uint8Array(bytesPerRow * h);
    for (let y = 0; y < h; y++) {
      for (let xb = 0; xb < bytesPerRow; xb++) {
        let b = 0; for (let bit = 0; bit < 8; bit++) {
          const x = xb * 8 + bit;
          let v = 0;
          if (x < w) {
            const idx = (y * w + x) * 4;
            const r = img[idx], g = img[idx+1], bl = img[idx+2];
            const gray = 0.299*r + 0.587*g + 0.114*bl;
            v = gray < threshold ? 1 : 0;
          }
          b |= v << (7 - bit);
        }
        out[y * bytesPerRow + xb] = b;
      }
    }
    return out;
  }

  // Code128-B (local drawer)
  const CODE128_PATTERNS = ['212222','222122','222221','121223','121322','131222','122213','122312','132212','221213','221312','231212','112232','122132','122231','113222','123122','123221','223211','221132','221231','213212','223112','312131','311222','321122','321221','312212','322112','322211','212123','212321','232121','111323','131123','131321','112313','132113','132311','211313','231113','231311','112133','112331','132131','113123','113321','133121','313121','211331','231131','213113','213311','213131','311123','311321','331121','312113','312311','332111','314111','221411','431111','111224','111422','121124','121421','141122','141221','112214','112412','122114','122411','142112','142211','241211','221114','413111','241112','134111','111242','121142','121241','114212','124112','124211','411212','421112','421211','212141','214121','412121','111143','111341','131141','114113','114311','411113','411311','113141','114131','311141','411131','211412','211214','211232','2331112'];
  const START_B = 104, STOP = 106;
  function code128BValues(text) { const vals=[START_B]; for (let i=0;i<text.length;i++){ const c=text.charCodeAt(i); if (c<32||c>126) throw new Error('Code128-B expects ASCII 32..126'); vals.push(c-32);} let sum=START_B; for (let i=1;i<vals.length;i++) sum+=vals[i]*i; vals.push(sum%103); vals.push(STOP); return vals; }
  function drawCode128ToCanvas(canvas, text, moduleW=2, height=70){ const ctx=canvas.getContext('2d'); const vals=code128BValues(text); let width=0; for (const v of vals){ for (const ch of CODE128_PATTERNS[v]) width+=parseInt(ch,10)*moduleW; } canvas.width=Math.max(2,width); canvas.height=height; ctx.fillStyle='#FFF'; ctx.fillRect(0,0,canvas.width,canvas.height); ctx.fillStyle='#000'; let x=0; for (const v of vals){ const pattern=CODE128_PATTERNS[v]; let bar=true; for (let i=0;i<pattern.length;i++){ const w=parseInt(pattern[i],10)*moduleW; if (bar) ctx.fillRect(x,0,w,height); x+=w; bar=!bar; } } }

  createApp({
    setup() {
      const printer = ref('');
      const direction = ref(0);
      const copies = ref(1);
      const leftMm = ref(0);
      const status = ref('');

      const fontFamily = ref('Sarabun, Noto Sans Thai, sans-serif');
      const fontSize = ref(24);
      const fontWeight = ref(600);
      const strokeWidth = ref(1);

      const stage = ref(null);
      const templates = ref([]);
      // Track selection reactively so buttons update reliably
      const selectionToken = ref(0);
      const hasSelection = computed(() => selectionToken.value > 0 && !!(stage.value && stage.value.getActiveObject()));

      function stageJSON() { return stage.value.toJSON(['selectable']); }

      function loadTemplates() {
        try { templates.value = JSON.parse(localStorage.getItem('a70pro_templates') || '[]'); } catch (_) { templates.value = []; }
      }
      function saveTemplate(name, preview) {
        const rec = { id: crypto.randomUUID(), name: name || 'Template', createdAt: Date.now(), preview: preview || '', fabric: stageJSON() };
        const arr = JSON.parse(localStorage.getItem('a70pro_templates') || '[]');
        arr.unshift(rec);
        localStorage.setItem('a70pro_templates', JSON.stringify(arr.slice(0, 50)));
        loadTemplates();
      }
      function deleteTemplate(t) {
        const arr = JSON.parse(localStorage.getItem('a70pro_templates') || '[]');
        const next = arr.filter(x => x.id !== t.id);
        localStorage.setItem('a70pro_templates', JSON.stringify(next));
        loadTemplates();
      }

      function enhance(obj) {
        obj.set({
          selectable: true,
          evented: true,
          hasControls: true,
          hasBorders: true,
          lockScalingFlip: false,
          lockScalingX: false,
          lockScalingY: false,
          lockRotation: false,
          transparentCorners: false,
          cornerStyle: 'rect',
          cornerColor: '#2563eb',
          borderColor: '#2563eb',
          cornerSize: 12,
          rotatingPointOffset: 30
        });
        if (obj.setControlsVisibility) obj.setControlsVisibility({ tl:true,tr:true,bl:true,br:true, ml:true,mr:true,mt:true,mb:true, mtr:true });
        return obj;
      }
      function ensureControls(obj) {
        if (!obj) return;
        obj.hasControls = true; obj.hasBorders = true; obj.selectable = true; obj.evented = true;
        if (obj.setControlsVisibility) obj.setControlsVisibility({ tl:true,tr:true,bl:true,br:true, mt:true,mb:true, ml:true,mr:true, mtr:true });
        if (obj.controls && obj.controls.mtr) obj.controls.mtr.visible = true;
        obj.perPixelTargetFind = false;
        obj.setCoords();
      }
      function addText() {
        const val = prompt('Text:', 'ทดสอบ / HELLO'); if (val == null) return;
        const tb = new fabric.Textbox(val, { left: 0, top: 0, fontFamily: fontFamily.value, fontSize: fontSize.value, fontWeight: String(fontWeight.value), fill: '#000', editable: true });
        enhance(tb); ensureControls(tb);
        stage.value.add(tb);
        stage.value.setActiveObject(tb);
        stage.value.requestRenderAll();
        selectionToken.value++;
      }
      function addBarcode() {
        const code = prompt('Code128 value (ASCII 32..126):', '1234567890'); if (code == null) return;
        const tmp = document.createElement('canvas');
        drawCode128ToCanvas(tmp, code, 2, 70);
        const img = new fabric.Image(tmp, { left: 0, top: 0, selectable: true });
        enhance(img); ensureControls(img);
        stage.value.add(img);
        stage.value.setActiveObject(img);
        stage.value.requestRenderAll();
        selectionToken.value++;
      }
      function addQR() {
        if (!window.qrcode) { alert('qrcode.min.js not found. Place it in examples/lib.'); return; }
        const text = prompt('QR content:', 'Hello'); if (text == null) return;
        const qr = window.qrcode(0, 'M'); qr.addData(text); qr.make();
        const modules = qr.getModuleCount(), scale=4, margin=0, size = modules*scale+margin*2*scale;
        const tmp = document.createElement('canvas'); tmp.width=size; tmp.height=size; const ctx=tmp.getContext('2d');
        ctx.fillStyle='#FFF'; ctx.fillRect(0,0,size,size); ctx.fillStyle='#000';
        for(let r=0;r<modules;r++){ for(let c=0;c<modules;c++){ if(qr.isDark(r,c)) ctx.fillRect(margin*scale + c*scale, margin*scale + r*scale, scale, scale); }}
        const img = new fabric.Image(tmp, { left: 0, top: 0, selectable: true });
        enhance(img); ensureControls(img);
        stage.value.add(img);
        stage.value.setActiveObject(img);
        stage.value.requestRenderAll();
        selectionToken.value++;
      }
      function onImageFile(e) {
        const f = e.target.files && e.target.files[0]; if (!f) return;
        const reader = new FileReader(); reader.onload = () => {
          fabric.Image.fromURL(reader.result, (img) => {
            const maxW = WIDTH_DOTS-2, maxH = HEIGHT_DOTS-2;
            let scale = 1; if (img.width>maxW || img.height>maxH) scale = Math.min(maxW/img.width, maxH/img.height);
            img.set({ left:0, top:0, scaleX: scale, scaleY: scale, selectable: true });
            enhance(img); ensureControls(img);
            stage.value.add(img);
            stage.value.setActiveObject(img);
            stage.value.requestRenderAll();
            selectionToken.value++;
          });
        }; reader.readAsDataURL(f);
        e.target.value = '';
      }
      function copySelected() {
        const obj = stage.value.getActiveObject(); if (!obj) return;
        obj.clone((cl) => {
          const margin=10; const newLeft = Math.min(WIDTH_DOTS - cl.getScaledWidth() - margin, obj.left + obj.getScaledWidth() + margin);
          cl.set({ left: Math.max(0, newLeft), top: obj.top });
          enhance(cl); ensureControls(cl);
          stage.value.add(cl);
          stage.value.setActiveObject(cl);
          stage.value.requestRenderAll();
          selectionToken.value++;
        });
      }
      function deleteSelected() {
        const obj = stage.value.getActiveObject();
        if (obj) {
          stage.value.remove(obj);
          stage.value.discardActiveObject();
          stage.value.requestRenderAll();
          // Force recompute for buttons
          selectionToken.value = 0;
        }
      }
      function applyTextToSelection() {
        const obj = stage.value.getActiveObject(); if (!obj || !obj.set) return;
        if (obj.type && obj.type.indexOf('text') !== -1) {
          obj.set({ fontFamily: fontFamily.value, fontSize: fontSize.value, fontWeight: String(fontWeight.value) }); stage.value.requestRenderAll();
        }
      }
      function applyStrokeToSelection() {
        const obj = stage.value.getActiveObject(); if (!obj || !obj.set) return;
        if (obj.type && ['line','rect','circle','triangle','path','image','textbox'].some(t => obj.type.includes(t))) {
          obj.set({ stroke: '#000', strokeWidth: strokeWidth.value }); stage.value.requestRenderAll();
        }
      }

      // Shapes
      function addLine() { const line = new fabric.Line([0,10,120,10], { left:0, top:0, stroke:'#000', strokeWidth: strokeWidth.value }); enhance(line); ensureControls(line); stage.value.add(line); stage.value.setActiveObject(line); stage.value.requestRenderAll(); selectionToken.value++; }
      function addRect() { const r = new fabric.Rect({ left:0, top:0, width:120, height:60, fill:'rgba(0,0,0,0)', stroke:'#000', strokeWidth: strokeWidth.value }); enhance(r); ensureControls(r); stage.value.add(r); stage.value.setActiveObject(r); stage.value.requestRenderAll(); selectionToken.value++; }
      function addCircle() { const c = new fabric.Circle({ left:0, top:0, radius:30, fill:'rgba(0,0,0,0)', stroke:'#000', strokeWidth: strokeWidth.value }); enhance(c); ensureControls(c); stage.value.add(c); stage.value.setActiveObject(c); stage.value.requestRenderAll(); selectionToken.value++; }
      function addTriangle() { const t = new fabric.Triangle({ left:0, top:0, width:80, height:60, fill:'rgba(0,0,0,0)', stroke:'#000', strokeWidth: strokeWidth.value }); enhance(t); ensureControls(t); stage.value.add(t); stage.value.setActiveObject(t); stage.value.requestRenderAll(); selectionToken.value++; }

      async function renderToBitmap(directionVal) {
        // render fabric to offscreen, optionally rotate 180 to keep WYSIWYG for direction=1
        stage.value.discardActiveObject(); stage.value.requestRenderAll();
        const off = document.createElement('canvas'); off.width = WIDTH_DOTS; off.height = HEIGHT_DOTS; const ctx = off.getContext('2d');
        ctx.fillStyle='#FFF'; ctx.fillRect(0,0,WIDTH_DOTS,HEIGHT_DOTS);
        const dataURL = stage.value.toDataURL({ format:'png', multiplier:1, enableRetinaScaling:false });
        await new Promise((resolve)=>{ const img=new Image(); img.onload=()=>{ ctx.drawImage(img,0,0); resolve(); }; img.src=dataURL; });
        // Swap: 0 means flip 180, 1 means as-is
        if (directionVal === 0) {
          const rot = document.createElement('canvas'); rot.width=WIDTH_DOTS; rot.height=HEIGHT_DOTS; const rctx=rot.getContext('2d');
          rctx.translate(WIDTH_DOTS, HEIGHT_DOTS); rctx.rotate(Math.PI); rctx.drawImage(off, 0, 0); return { canvas: rot, sendDirection: 0 };
        }
        return { canvas: off, sendDirection: 0 };
      }

      async function onPrint() {
        try {
          status.value = 'Rendering…';
          const { canvas, sendDirection } = await renderToBitmap(direction.value);
          const bytes = pack1bppFromCanvas(canvas, 200);
          const bytesBase64 = btoa(String.fromCharCode(...bytes));
          const payload = {
            printer: (printer.value||undefined),
            widthDots: WIDTH_DOTS, heightDots: HEIGHT_DOTS, bytesBase64,
            xDots: 0, yDots: 0,
            sizeMm: { w: 60, h: 20 }, gapMm: { h: 2, off: 0 },
            direction: sendDirection, speed: 4, density: 8,
            copies: Math.max(1, Number(copies.value||1)),
            leftMm: isFinite(leftMm.value) ? leftMm.value : undefined,
            invert: true
          };
          const res = await fetch('http://localhost:7788/print/bitmap', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload) });
          const data = await res.json();
          status.value = res.ok ? ('Printed ' + (data.job||'')) : (data.error||'Error');

          // Save template (with preview)
          const preview = canvas.toDataURL('image/png');
          const name = 'Label ' + new Date().toLocaleString();
          saveTemplate(name, preview);
        } catch (e) {
          status.value = e.message || String(e);
        }
      }

      function loadTemplate(t) {
        try { stage.value.loadFromJSON(t.fabric, () => { stage.value.renderAll(); }); } catch (e) { alert('Failed to load template'); }
      }
      async function printTemplate(t) {
        loadTemplate(t);
        await new Promise(r => setTimeout(r, 50));
        onPrint();
      }

      function newCanvas() {
        if (!stage.value) return;
        const objs = stage.value.getObjects();
        objs.slice().forEach(o => stage.value.remove(o));
        stage.value.discardActiveObject();
        stage.value.requestRenderAll();
      }

      function addShape(type){ if(type==='line') addLine(); else if(type==='rect') addRect(); else if(type==='circle') addCircle(); else if(type==='triangle') addTriangle(); }

      onMounted(() => {
        // Global control styles for consistent UX
        fabric.Object.prototype.transparentCorners = false;
        fabric.Object.prototype.cornerStyle = 'rect';
        fabric.Object.prototype.cornerColor = '#2563eb';
        fabric.Object.prototype.borderColor = '#2563eb';
        fabric.Object.prototype.cornerSize = 12;
        fabric.Object.prototype.rotatingPointOffset = 30;

        stage.value = new fabric.Canvas('stage', { backgroundColor: '#fff', selection: true, preserveObjectStacking: true, enableRetinaScaling: false });
        stage.value.setWidth(WIDTH_DOTS); stage.value.setHeight(HEIGHT_DOTS);
        // Ensure no viewport transform and consistent zoom for pointer accuracy
        stage.value.setZoom(1);
        stage.value.viewportTransform = [1,0,0,1,0,0];
        // Update selection token reactively on selection changes
        stage.value.on('selection:created', (e) => { ensureControls(stage.value.getActiveObject()); selectionToken.value++; });
        stage.value.on('selection:updated', (e) => { ensureControls(stage.value.getActiveObject()); selectionToken.value++; });
        stage.value.on('selection:cleared', () => { selectionToken.value = 0; });
        stage.value.on('object:removed', () => { selectionToken.value = stage.value.getActiveObject() ? selectionToken.value+1 : 0; });
        stage.value.on('object:added', (e) => { if (e && e.target) e.target.setCoords(); selectionToken.value++; });
        // Keyboard delete: Delete/Backspace/Enter (when not editing text)
        window.addEventListener('keydown', (ev) => {
          const key = ev.key;
          if (!['Delete', 'Backspace', 'Enter'].includes(key)) return;
          const ao = stage.value.getActiveObject();
          if (!ao) return;
          if (ao.type && ao.type.indexOf('text') !== -1 && ao.isEditing) return;
          ev.preventDefault();
          deleteSelected();
        });
        loadTemplates();
      });

      function deleteTemplate(t) {
        const arr = JSON.parse(localStorage.getItem('a70pro_templates') || '[]');
        const next = arr.filter(x => x.id !== t.id);
        localStorage.setItem('a70pro_templates', JSON.stringify(next));
        loadTemplates();
      }

      return {
        printer, direction, copies, leftMm, status,
        fontFamily, fontSize, fontWeight, strokeWidth,
        templates, hasSelection,
        onPrint, loadTemplate, printTemplate, deleteTemplate,
        addText, addBarcode, addQR, onImageFile, copySelected, deleteSelected,
        applyTextToSelection, applyStrokeToSelection,
        addShape,
        newCanvas
      };
    }
  }).mount('#app');
  </script>
</body>
</html>
