<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>60x20 Label (Bitmap → Bridge)</title>
  <style>
    body { font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif; margin: 20px; }
    .row { margin-bottom: 10px; display: flex; gap: 8px; align-items: center; flex-wrap: wrap; }
    label { min-width: 120px; }
    input[type="text"], input[type="number"], select { padding: 6px 8px; }
    button { padding: 8px 12px; cursor: pointer; }
    canvas { border: 1px solid #ccc; display: block; margin-top: 10px; image-rendering: pixelated; }
    small { color: #555; }
  </style>
  <!-- Offline: rely on system fonts; install Thai fonts locally if needed. -->
</head>
<body>
  <h1>60×20 mm Label (203 dpi)</h1>
  <div class="row"><small>Bridge must be running at http://localhost:7788 (npm run serve)</small></div>

  <div class="row">
    <label>Thai text</label>
    <input id="thai" type="text" value="ทดสอบ ฉลาก" size="32" />
  </div>
  <div class="row">
    <label>English text</label>
    <input id="eng" type="text" value="HELLO 123456" size="32" />
  </div>
  <div class="row">
    <label>Barcode (Code128)</label>
    <input id="barcode" type="text" value="1234567890" size="24" />
  </div>
  <div class="row">
    <label>Copies</label>
    <input id="copies" type="number" value="1" min="1" step="1" style="width: 80px;" />
    <label>Direction</label>
    <select id="direction">
      <option value="0">0 (facing front)</option>
      <option value="1" selected>1 (flip 180°)</option>
    </select>
    <label>Printer</label>
    <input id="printer" type="text" placeholder="optional (queue name)" size="22" />
  </div>
  <div class="row">
    <label>Font family</label>
    <select id="fontFamily">
      <option value="Sarabun" selected>Sarabun (Thai, looped)</option>
      <option value="Noto Sans Thai">Noto Sans Thai</option>
      <option value="system">System Sans</option>
    </select>
    <label>Thai size</label>
    <input id="thaiSize" type="number" value="28" min="12" max="48" step="1" style="width:80px;" />
    <label>English size</label>
    <input id="engSize" type="number" value="18" min="10" max="40" step="1" style="width:80px;" />
    <label>Weight</label>
    <input id="fontWeight" type="number" value="600" min="100" max="900" step="50" style="width:90px;" />
  </div>
  <div class="row">
    <button id="render">Render</button>
    <button id="print">Print (bitmap → bridge)</button>
    <span id="status"></span>
  </div>

  <canvas id="preview" width="480" height="160"></canvas>

  <script>
    // 60x20 mm at 203 dpi → 480x160 dots
    const DPI = 203;
    const WIDTH_DOTS = Math.round(60 * DPI / 25.4);   // 480
    const HEIGHT_DOTS = Math.round(20 * DPI / 25.4);  // 160

    const $ = (id) => document.getElementById(id);
    const canvas = $('preview');
    const ctx = canvas.getContext('2d');

    function pack1bppFromCanvas(canvas, threshold = 180) {
      const w = canvas.width, h = canvas.height;
      const ctx = canvas.getContext('2d');
      const img = ctx.getImageData(0, 0, w, h).data;
      const bytesPerRow = Math.ceil(w / 8);
      const out = new Uint8Array(bytesPerRow * h);
      for (let y = 0; y < h; y++) {
        for (let xb = 0; xb < bytesPerRow; xb++) {
          let b = 0;
          for (let bit = 0; bit < 8; bit++) {
            const x = xb * 8 + bit;
            let v = 0;
            if (x < w) {
              const idx = (y * w + x) * 4;
              const r = img[idx], g = img[idx+1], bl = img[idx+2];
              const gray = 0.299*r + 0.587*g + 0.114*bl;
              v = gray < threshold ? 1 : 0; // 1 = black
            }
            b |= v << (7 - bit);
          }
          out[y * bytesPerRow + xb] = b;
        }
      }
      return out;
    }

    // Minimal Code128-B encoder/drawer (ASCII 32..126)
    // Patterns from ISO/IEC 15417: each entry is 6 module widths followed by a bar, total 11 modules.
    const CODE128_PATTERNS = [
      '212222','222122','222221','121223','121322','131222','122213','122312','132212','221213',
      '221312','231212','112232','122132','122231','113222','123122','123221','223211','221132',
      '221231','213212','223112','312131','311222','321122','321221','312212','322112','322211',
      '212123','212321','232121','111323','131123','131321','112313','132113','132311','211313',
      '231113','231311','112133','112331','132131','113123','113321','133121','313121','211331',
      '231131','213113','213311','213131','311123','311321','331121','312113','312311','332111',
      '314111','221411','431111','111224','111422','121124','121421','141122','141221','112214',
      '112412','122114','122411','142112','142211','241211','221114','413111','241112','134111',
      '111242','121142','121241','114212','124112','124211','411212','421112','421211','212141',
      '214121','412121','111143','111341','131141','114113','114311','411113','411311','113141',
      '114131','311141','411131','211412','211214','211232','2331112' // 106 is stop (13 modules)
    ];
    const START_B = 104;
    const STOP = 106;

    function code128BValues(text) {
      const vals = [START_B];
      for (let i = 0; i < text.length; i++) {
        const c = text.charCodeAt(i);
        if (c < 32 || c > 126) throw new Error('Code128-B supports ASCII 32..126');
        vals.push(c - 32);
      }
      // checksum
      let sum = START_B;
      for (let i = 1; i < vals.length; i++) sum += vals[i] * i;
      vals.push(sum % 103);
      vals.push(STOP);
      return vals;
    }

    function drawCode128(ctx, x, y, height, moduleW, text) {
      const vals = code128BValues(text);
      let cx = x;
      for (let vi = 0; vi < vals.length; vi++) {
        const pattern = CODE128_PATTERNS[vals[vi]];
        let bar = true; // start with bar
        for (let i = 0; i < pattern.length; i++) {
          const w = parseInt(pattern[i], 10) * moduleW;
          if (bar) ctx.fillRect(cx, y, w, height);
          cx += w;
          bar = !bar;
        }
      }
      return cx - x; // total width
    }

    function renderLabel() {
      const thai = $('thai').value;
      const eng = $('eng').value;
      const barcode = $('barcode').value;

      // Clear
      ctx.fillStyle = '#fff';
      ctx.fillRect(0, 0, WIDTH_DOTS, HEIGHT_DOTS);
      ctx.fillStyle = '#000';

      // Font settings from controls
      const familySel = document.getElementById('fontFamily').value;
      const family = familySel === 'system' ? 'sans-serif' : `"${familySel}", "Noto Sans Thai", sans-serif`;
      const thaiSize = Math.max(10, parseInt(document.getElementById('thaiSize').value, 10) || 28);
      const engSize = Math.max(8, parseInt(document.getElementById('engSize').value, 10) || 18);
      const weight = Math.min(900, Math.max(100, parseInt(document.getElementById('fontWeight').value, 10) || 600));

      // Top Thai text
      ctx.font = `${weight} ${thaiSize}px ${family}`;
      ctx.fillText(thai, 10, 42);

      // Barcode area
      const moduleW = 2; // 2-dot module for decent width
      const barcodeH = 70; // fit within 20mm
      const bw = drawCode128(ctx, 10, 55, barcodeH, moduleW, barcode);

      // Human-readable text under barcode (if space)
      ctx.font = `${weight} ${engSize}px ${family}`;
      ctx.fillText(eng, 10, 55 + barcodeH + 18);
    }

    async function printLabel() {
      const dSel = parseInt($('direction').value, 10);
      const direction = Number.isNaN(dSel) ? 1 : dSel; // honor 0 and 1 exactly
      const copies = Math.max(1, parseInt($('copies').value, 10) || 1);
      const printer = $('printer').value.trim();
      const bytes = pack1bppFromCanvas(canvas, 200);
      const bytesBase64 = btoa(String.fromCharCode(...bytes));
      const payload = {
        printer: printer || undefined,
        widthDots: WIDTH_DOTS,
        heightDots: HEIGHT_DOTS,
        bytesBase64,
        xDots: 0,
        yDots: 0,
        sizeMm: { w: 60, h: 20 },
        gapMm: { h: 2, off: 0 },
        direction,
        speed: 4,
        density: 8,
        copies,
        invert: true
      };
      $('status').textContent = 'Printing…';
      try {
        const res = await fetch('http://localhost:7788/print/bitmap', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
        const data = await res.json();
        $('status').textContent = res.ok ? ('Done ' + (data.job || '')) : (data.error || 'Error');
        console.log(data);
      } catch (e) {
        $('status').textContent = e.message || String(e);
      }
    }

    $('render').addEventListener('click', renderLabel);
    $('print').addEventListener('click', () => { renderLabel(); printLabel(); });
    // Initial render
    renderLabel();
  </script>
</body>
</html>
