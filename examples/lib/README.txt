Place the following files in this folder to run the editor fully offline:

Required
- fabric.min.js
  - Download from: https://cdn.jsdelivr.net/npm/fabric@5.3.0/dist/fabric.min.js

- qrcode.min.js (qrcode-generator)
  - Download from: https://cdn.jsdelivr.net/npm/qrcode-generator@2.0.4/dist/qrcode.min.js

Optional (fonts)
- Install Thai fonts on your OS (e.g., Sarabun, Noto Sans Thai) for best rendering on canvas.

Notes
- After placing the files, open examples/editor.html. The page loads ./lib/fabric.min.js and ./lib/qrcode.min.js locally — no internet needed.
