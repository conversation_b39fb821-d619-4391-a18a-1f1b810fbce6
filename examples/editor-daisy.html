<!doctype html>
<html lang="en" data-theme="light">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>A70Pro Label Editor (Vue + DaisyUI)</title>
  <!-- Tailwind v4 CDN -->
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <!-- DaisyUI v5 (prebuilt CSS) -->
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <!-- Vue 3 -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
  <!-- Local libs (place files in examples/lib) -->
  <script src="./lib/fabric.min.js"></script>
  <script src="./lib/qrcode.min.js"></script>
  <style>
    body { font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, 'Noto Sans Thai', sans-serif; }
    #stage { width: 480px; height: 160px; image-rendering: pixelated; touch-action: none; }
  </style>
</head>
<body>
  <div id="app" class="min-h-screen p-4">
    <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- Left: Print Settings only -->
      <div class="space-y-4">
        <div class="card bg-base-100 shadow">
          <div class="card-body">
            <h2 class="card-title">Print Settings</h2>
            <div class="form-control">
              <label class="label"><span class="label-text">Printer</span></label>
              <div class="flex items-center justify-between">
                <button class="btn btn-sm" @click="refreshPrinters">Refresh</button>
                <span class="text-sm text-base-content/60">{{ printers.length }} found</span>
              </div>
              <div class="mt-2 max-h-48 overflow-auto space-y-1">
                <button v-for="p in printers" :key="p.name" type="button"
                        class="btn btn-sm w-full justify-between"
                        :class="{ 'btn-success': selectedPrinter===p.name, 'btn-ghost': selectedPrinter!==p.name }"
                        @click="selectPrinter(p.name)">
                  <span class="flex items-center gap-2">
                    <span class="w-2 h-2 rounded-full" :class="stateDot(p.state)"></span>
                    <span class="truncate">{{ p.name }}<span v-if="p.isDefault"> (default)</span></span>
                  </span>
                  <span class="text-xs opacity-70">{{ p.state }}</span>
                </button>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-3 mt-2">
              <div class="form-control">
                <label class="label"><span class="label-text">Copies</span></label>
                <input type="number" min="1" v-model.number="copies" class="input input-bordered input-sm" />
              </div>
              <div class="form-control">
                <label class="label"><span class="label-text">Collation</span></label>
                <select v-model="collation" class="select select-bordered select-sm">
                  <option value="collated">Collated</option>
                  <option value="uncollated">Uncollated</option>
                </select>
              </div>
            </div>
            <div class="mt-2 form-control">
              <label class="cursor-pointer label">
                <span class="label-text">Reverse order</span>
                <input type="checkbox" v-model="reverse" class="toggle" />
              </label>
            </div>
            <div class="mt-2 flex gap-2">
              <button class="btn" @click="saveTemplate">Save</button>
              <button class="btn btn-primary" :disabled="!canPrint" @click="onPrint">Print</button>
            </div>
            <div class="mt-2 text-sm opacity-70">{{ status }}</div>
          </div>
        </div>
      </div>

      <!-- Right: Elements, Canvas, Data, Bindings, Selection, Templates -->
      <div class="lg:col-span-2 space-y-4">
        <!-- Add Elements -->
        <div class="card bg-base-100 shadow">
          <div class="card-body">
            <h2 class="card-title">Add Elements</h2>
            <div class="flex flex-wrap gap-2">
              <button class="btn" @click="addText">Text</button>
              <button class="btn" @click="addBarcode">Barcode</button>
              <button class="btn" @click="addQR">QR</button>
              <label class="btn" for="imgFile">Image</label>
              <input id="imgFile" type="file" accept="image/*" class="hidden" @change="onImageFile" />
              <button class="btn" @click="addLine">Line</button>
              <button class="btn" @click="addRect">Rect</button>
              <button class="btn" @click="addCircle">Circle</button>
              <button class="btn" @click="addTriangle">Triangle</button>
              <div class="divider lg:divider-horizontal"></div>
              <button class="btn" @click="undo">Undo</button>
              <button class="btn" :disabled="!hasSelection" @click="copySelected">Copy</button>
              <button class="btn" :disabled="!hasSelection" @click="deleteSelected">Delete</button>
              <button class="btn btn-accent" @click="clearCanvas">Clear Label</button>
            </div>
          </div>
        </div>

        <!-- Canvas -->
        <div class="card bg-base-100 shadow">
          <div class="card-body items-center">
            <div id="stageWrap" class="inline-block border rounded bg-white">
              <canvas id="stage" width="480" height="160"></canvas>
            </div>
          </div>
        </div>

        <!-- Data JSON -->
        <div class="card bg-base-100 shadow">
          <div class="card-body">
            <h2 class="card-title">Data (JSON Array)</h2>
            <textarea v-model="dataJSON" rows="6" class="textarea textarea-bordered font-mono text-xs"></textarea>
            <div class="flex items-center gap-3 mt-2">
              <label class="label"><span class="label-text">Preview row</span></label>
              <input type="number" min="1" v-model.number="previewIndex" class="input input-bordered input-sm w-24" />
              <button class="btn" @click="applyPreview">Apply Preview</button>
              <span class="text-sm opacity-70">{{ dataStatus }}</span>
            </div>
          </div>
        </div>

        <!-- Bindings -->
        <div class="card bg-base-100 shadow">
          <div class="card-body">
            <h2 class="card-title">Bindings (All Elements)</h2>
            <div class="max-h-56 overflow-auto space-y-2">
              <div v-if="objects.length===0" class="text-sm opacity-70">No elements. Add Text/Barcode/QR to bind.</div>
              <div v-for="(o, idx) in objects" :key="o.__uid || idx" class="flex items-center gap-2">
                <div class="w-32 text-xs truncate" :title="bindingPreview(o)">{{ labelOf(o, idx) }}</div>
                <template v-if="isBindable(o)">
                  <select class="select select-bordered select-sm w-48"
                          v-model="o._bindingKey" :disabled="dataKeys.length===0">
                    <option value="">(none)</option>
                    <option v-for="k in dataKeys" :key="k" :value="k">{{ k }}</option>
                  </select>
                  <button class="btn btn-sm" @click="selectObject(o)">Select</button>
                </template>
                <template v-else>
                  <div class="text-xs opacity-60">(not bindable)</div>
                </template>
              </div>
            </div>
          </div>
        </div>

        <!-- Selection Settings -->
        <div class="card bg-base-100 shadow">
          <div class="card-body">
            <h2 class="card-title">Selection Settings</h2>
            <div class="grid grid-cols-2 gap-3 items-center">
              <label class="label"><span class="label-text">Font family</span></label>
              <select v-model="fontFamily" class="select select-bordered select-sm">
                <option value="Sarabun, Noto Sans Thai, sans-serif">Sarabun/Noto/System</option>
                <option value="Noto Sans Thai, sans-serif">Noto Sans Thai</option>
                <option value="sans-serif">System Sans</option>
              </select>
              <label class="label"><span class="label-text">Font size</span></label>
              <input type="number" v-model.number="fontSize" class="input input-bordered input-sm" />
              <label class="label"><span class="label-text">Font weight</span></label>
              <input type="number" v-model.number="fontWeight" step="50" class="input input-bordered input-sm" />
              <label class="label"><span class="label-text">Stroke width</span></label>
              <input type="number" v-model.number="strokeWidth" step="0.5" class="input input-bordered input-sm" />
            </div>
            <div class="mt-2 flex gap-2">
              <button class="btn" @click="applyTextToSelection">Apply Text</button>
              <button class="btn" @click="applyStrokeToSelection">Apply Stroke</button>
            </div>
          </div>
        </div>

        <!-- Templates -->
        <div class="card bg-base-100 shadow">
          <div class="card-body">
            <h2 class="card-title">Templates</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div class="card bg-base-200" v-for="t in templates" :key="t.id">
                <div class="card-body">
                  <div class="text-xs opacity-70">{{ new Date(t.createdAt).toLocaleString() }}</div>
                  <div class="font-medium truncate" :title="t.name">{{ t.name }}</div>
                  <img :src="t.preview" class="mt-1 border rounded" />
                  <div class="card-actions justify-end">
                    <button class="btn btn-sm" @click="loadTemplate(t)">Load</button>
                    <button class="btn btn-sm btn-error" @click="deleteTemplate(t)">Delete</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
  const { createApp, ref, computed, onMounted } = Vue;

  // Helper: 1-bpp pack
  function pack1bppFromCanvas(canvas, threshold = 200) {
    const w = canvas.width, h = canvas.height;
    const ctx = canvas.getContext('2d');
    const img = ctx.getImageData(0, 0, w, h).data;
    const bytesPerRow = Math.ceil(w / 8);
    const out = new Uint8Array(bytesPerRow * h);
    for (let y = 0; y < h; y++) {
      for (let xb = 0; xb < bytesPerRow; xb++) {
        let b = 0;
        for (let bit = 0; bit < 8; bit++) {
          const x = xb * 8 + bit;
          let v = 0;
          if (x < w) {
            const idx = (y * w + x) * 4;
            const r = img[idx], g = img[idx+1], bl = img[idx+2];
            const gray = 0.299*r + 0.587*g + 0.114*bl;
            v = gray < threshold ? 1 : 0;
          }
          b |= v << (7 - bit);
        }
        out[y * bytesPerRow + xb] = b;
      }
    }
    return out;
  }

  // Code128 (subset B) drawer
  const CODE128_PATTERNS = ['212222','222122','222221','121223','121322','131222','122213','122312','132212','221213','221312','231212','112232','122132','122231','113222','123122','123221','223211','221132','221231','213212','223112','312131','311222','321122','321221','312212','322112','322211','212123','212321','232121','111323','131123','131321','112313','132113','132311','211313','231113','231311','112133','112331','132131','113123','113321','133121','313121','211331','231131','213113','213311','213131','311123','311321','331121','312113','312311','332111','314111','221411','431111','111224','111422','121124','121421','141122','141221','112214','112412','122114','122411','142112','142211','241211','221114','413111','241112','134111','111242','121142','121241','114212','124112','124211','411212','421112','421211','212141','214121','412121','111143','111341','131141','114113','114311','411113','411311','113141','114131','311141','411131','211412','211214','211232','2331112'];
  const START_B = 104, STOP = 106;
  function code128BValues(text) { const vals=[START_B]; for (let i=0;i<text.length;i++){ const c=text.charCodeAt(i); if (c<32||c>126) throw new Error('Code128-B expects ASCII 32..126'); vals.push(c-32);} let sum=START_B; for (let i=1;i<vals.length;i++) sum+=vals[i]*i; vals.push(sum%103); vals.push(STOP); return vals; }
  function drawCode128ToCanvas(canvas, text, moduleW=2, height=70){ const ctx=canvas.getContext('2d'); const vals=code128BValues(text); let width=0; for (const v of vals){ for (const ch of CODE128_PATTERNS[v]) width+=parseInt(ch,10)*moduleW; } canvas.width=Math.max(2,width); canvas.height=height; ctx.fillStyle='#FFF'; ctx.fillRect(0,0,canvas.width,canvas.height); ctx.fillStyle='#000'; let x=0; for (const v of vals){ const pattern=CODE128_PATTERNS[v]; let bar=true; for (let i=0;i<pattern.length;i++){ const w=parseInt(pattern[i],10)*moduleW; if (bar) ctx.fillRect(x,0,w,height); x+=w; bar=!bar; } } }

  async function ensureQRCode() {
    if (window.qrcode) return;
    // try local fallback in /examples
    await new Promise((resolve, reject) => {
      const s = document.createElement('script'); s.src = './qrcode.min.js'; s.onload = resolve; s.onerror = () => resolve(); document.head.appendChild(s);
    });
  }

  createApp({
    setup() {
      const DPI = 203, WIDTH_DOTS = 480, HEIGHT_DOTS = 160;

      // UI state
      const printers = ref([]);
      const selectedPrinter = ref('');
      const status = ref('');
      const copies = ref(1);
      const collation = ref('collated');
      const reverse = ref(false);

      // Data + bindings
      const dataJSON = ref('');
      const previewIndex = ref(1);
      const dataStatus = ref('');

      // Stage and templates
      const stage = ref(null);
      const templates = ref([]);
      const TKEY = 'a70pro_templates_daisy';

      // Selection controls
      const fontFamily = ref('Sarabun, Noto Sans Thai, sans-serif');
      const fontSize = ref(24);
      const fontWeight = ref(600);
      const strokeWidth = ref(1);

      // Derived
      const objects = computed(() => stage.value ? stage.value.getObjects() : []);
      const hasSelection = computed(() => !!(stage.value && stage.value.getActiveObject()));
      const dataKeys = computed(() => {
        try { const arr = JSON.parse(dataJSON.value||'[]'); if (!Array.isArray(arr)) return []; const keys = new Set(); arr.slice(0,50).forEach(r => r && typeof r==='object' && Object.keys(r).forEach(k => keys.add(k))); return Array.from(keys); } catch (_) { return []; }
      });
      const canPrint = computed(() => {
        const onlineStates = new Set(['idle','printing']);
        const online = printers.value.some(p => p.name===selectedPrinter.value && onlineStates.has(p.state));
        const hasObjs = (objects.value||[]).length > 0;
        return online && hasObjs;
      });

      function stateDot(state) {
        if (state==='idle') return 'bg-green-500';
        if (state==='printing') return 'bg-amber-500';
        if (state==='offline' || state==='disabled') return 'bg-rose-500';
        return 'bg-slate-400';
      }
      function labelOf(o, idx) {
        const k = objKind(o);
        return `${String(idx+1).padStart(2,'0')} • ${k}`;
      }
      function objKind(o) {
        if (o._kind === 'barcode') return 'Barcode';
        if (o._kind === 'qr') return 'QR';
        const t = String(o.type||'');
        if (t.includes('text')) return 'Text';
        if (t.includes('line')) return 'Line';
        if (t.includes('rect')) return 'Rect';
        if (t.includes('circle')) return 'Circle';
        if (t.includes('triangle')) return 'Triangle';
        if (t.includes('image')) return 'Image';
        return t || 'Object';
      }
      function isBindable(o) {
        const k = objKind(o); return k==='Text' || k==='Barcode' || k==='QR';
      }
      function bindingPreview(o) {
        const k = objKind(o);
        if (k==='Text') return (o.text||'').toString().slice(0,20);
        if (k==='Barcode' || k==='QR') return o._templateValue || '';
        return '';
      }
      function selectPrinter(name) { selectedPrinter.value = name; }

      async function refreshPrinters() {
        try {
          const res = await fetch('http://localhost:7788/printers/status');
          const data = await res.json();
          printers.value = data.printers || [];
          // pick default
          if (!selectedPrinter.value && printers.value.length) {
            const preferred = printers.value.find(p => p.name==='AiYin_A70Pro') || printers.value.find(p => /aiyin|a70/i.test(p.name)) || printers.value.find(p => p.isDefault) || printers.value[0];
            selectedPrinter.value = preferred ? preferred.name : '';
          }
        } catch (e) { printers.value = []; }
      }

      // Enhance fabric
      function enhance(obj) {
        obj.set({ selectable: true, evented: true, hasControls: true, hasBorders: true, lockScalingFlip: false, transparentCorners: false });
        if (obj.setControlsVisibility) obj.setControlsVisibility({ tl:true,tr:true,bl:true,br:true, mt:true,mb:true, ml:true,mr:true, mtr:true });
        return obj;
      }

      function addText() {
        const val = prompt('Text:', 'ทดสอบ / HELLO'); if (val == null) return;
        const tb = new fabric.Textbox(val, { left: 5, top: 5, fontFamily: fontFamily.value, fontSize: fontSize.value, fontWeight: String(fontWeight.value), fill: '#000', editable: true });
        tb._templateText = val; tb._bindingKey = '';
        enhance(tb); stage.value.add(tb); stage.value.setActiveObject(tb); stage.value.requestRenderAll(); snapshot();
      }
      function addBarcode() {
        const code = prompt('Code128 value (ASCII 32..126):', '1234567890'); if (code == null) return;
        const tmp = document.createElement('canvas');
        drawCode128ToCanvas(tmp, code, 2, 70);
        const img = new fabric.Image(tmp, { left: 5, top: 5, selectable: true });
        img._kind='barcode'; img._bindingKey=''; img._templateValue=String(code||'');
        enhance(img); stage.value.add(img); stage.value.setActiveObject(img); stage.value.requestRenderAll(); snapshot();
      }
      async function addQR() {
        await ensureQRCode();
        const text = prompt('QR content:', 'Hello'); if (text == null) return;
        const qr = window.qrcode(0, 'M'); qr.addData(text); qr.make();
        const modules = qr.getModuleCount(), scale=4, margin=0, size = modules*scale+margin*2*scale;
        const tmp = document.createElement('canvas'); tmp.width=size; tmp.height=size; const ctx=tmp.getContext('2d');
        ctx.fillStyle='#FFF'; ctx.fillRect(0,0,size,size); ctx.fillStyle='#000';
        for (let r=0;r<modules;r++){ for (let c=0;c<modules;c++){ if (qr.isDark(r,c)) ctx.fillRect(margin*scale+c*scale, margin*scale+r*scale, scale, scale); }}
        const img = new fabric.Image(tmp, { left: 5, top: 5, selectable: true });
        img._kind='qr'; img._bindingKey=''; img._templateValue=String(text||'');
        enhance(img); stage.value.add(img); stage.value.setActiveObject(img); stage.value.requestRenderAll(); snapshot();
      }
      function onImageFile(e) {
        const f = e.target.files && e.target.files[0]; if (!f) return; const reader=new FileReader(); reader.onload=()=>{
          fabric.Image.fromURL(reader.result, (img) => {
            const maxW = WIDTH_DOTS-2, maxH = HEIGHT_DOTS-2; let scale=1; if (img.width>maxW||img.height>maxH) scale=Math.min(maxW/img.width, maxH/img.height);
            img.set({ left:0, top:0, scaleX:scale, scaleY:scale, selectable:true }); enhance(img);
            stage.value.add(img); stage.value.setActiveObject(img); stage.value.requestRenderAll(); snapshot();
          });
        }; reader.readAsDataURL(f); e.target.value='';
      }
      function addLine() { const line=new fabric.Line([0,10,120,10], { left:0, top:0, stroke:'#000', strokeWidth: strokeWidth.value }); enhance(line); stage.value.add(line); stage.value.setActiveObject(line); stage.value.requestRenderAll(); snapshot(); }
      function addRect() { const r=new fabric.Rect({ left:0, top:0, width:120, height:60, fill:'rgba(0,0,0,0)', stroke:'#000', strokeWidth: strokeWidth.value }); enhance(r); stage.value.add(r); stage.value.setActiveObject(r); stage.value.requestRenderAll(); snapshot(); }
      function addCircle() { const c=new fabric.Circle({ left:0, top:0, radius:30, fill:'rgba(0,0,0,0)', stroke:'#000', strokeWidth: strokeWidth.value }); enhance(c); stage.value.add(c); stage.value.setActiveObject(c); stage.value.requestRenderAll(); snapshot(); }
      function addTriangle() { const t=new fabric.Triangle({ left:0, top:0, width:80, height:60, fill:'rgba(0,0,0,0)', stroke:'#000', strokeWidth: strokeWidth.value }); enhance(t); stage.value.add(t); stage.value.setActiveObject(t); stage.value.requestRenderAll(); snapshot(); }

      function copySelected() { const obj=stage.value.getActiveObject(); if(!obj) return; obj.clone((cl)=>{ const margin=10; const newLeft=Math.min(WIDTH_DOTS-cl.getScaledWidth()-margin, obj.left+obj.getScaledWidth()+margin); cl.set({ left:Math.max(0,newLeft), top:obj.top }); enhance(cl); stage.value.add(cl); stage.value.setActiveObject(cl); stage.value.requestRenderAll(); snapshot(); }); }
      function deleteSelected() { const obj=stage.value.getActiveObject(); if (!obj) return; stage.value.remove(obj); stage.value.discardActiveObject(); stage.value.requestRenderAll(); snapshot(); }
      function clearCanvas() { const objs=stage.value.getObjects(); objs.slice().forEach(o=>stage.value.remove(o)); stage.value.discardActiveObject(); stage.value.requestRenderAll(); snapshot(); }
      function applyTextToSelection(){ const obj=stage.value.getActiveObject(); if (!obj || !(obj.type||'').includes('text')) return; obj.set({ fontFamily: fontFamily.value, fontSize: fontSize.value, fontWeight: String(fontWeight.value) }); stage.value.requestRenderAll(); }
      function applyStrokeToSelection(){ const obj=stage.value.getActiveObject(); if (!obj) return; obj.set({ stroke:'#000', strokeWidth: strokeWidth.value, strokeUniform:true }); stage.value.requestRenderAll(); }

      // History
      const HISTORY_LIMIT = 50; let history=[]; let historyLock=false;
      function stageJSON() { return stage.value.toJSON(['selectable','_bindingKey','_kind','_templateText','_templateValue']); }
      function snapshot(){ if(historyLock) return; const json=JSON.stringify(stageJSON()); if(!history.length||history[history.length-1]!==json){ history.push(json); if(history.length>HISTORY_LIMIT) history.shift(); } }
      function undo(){ if(history.length<=1) return; history.pop(); const prev=history[history.length-1]; historyLock=true; stage.value.loadFromJSON(prev, ()=>{ stage.value.renderAll(); historyLock=false; }); }

      // Data preview
      async function applyPreview(){ try{ const arr=JSON.parse(dataJSON.value||'[]'); if(!Array.isArray(arr)||arr.length===0){ dataStatus.value='Expected a non-empty array'; return; } const idx=Math.max(1, Number(previewIndex.value||1)); const rec = arr[idx-1] || arr[0]; await applyBindingsFor(rec); stage.value.requestRenderAll(); dataStatus.value = `Preview row ${idx}/${arr.length}`; } catch(e){ dataStatus.value='Invalid JSON array'; } }

      async function applyBindingsFor(record){ const objs=stage.value.getObjects(); for(const obj of objs){ if(obj._bindingKey){ const val = record && Object.prototype.hasOwnProperty.call(record, obj._bindingKey) ? record[obj._bindingKey] : ''; if ((obj.type||'').includes('text')) { if (!obj._templateText && typeof obj.text==='string') obj._templateText=obj.text; obj.text = String(val ?? ''); } else if (obj._kind==='barcode'){ const tmp=document.createElement('canvas'); try { drawCode128ToCanvas(tmp, String(val??''), 2, 70); } catch(e){ drawCode128ToCanvas(tmp, String(val??''), 2, 70); } const props={ left:obj.left, top:obj.top, scaleX:obj.scaleX, scaleY:obj.scaleY, angle:obj.angle, selectable:true }; const img=new fabric.Image(tmp, props); img._kind='barcode'; img._bindingKey=obj._bindingKey; img._templateValue=String(val??''); stage.value.remove(obj); stage.value.add(img); } else if (obj._kind==='qr'){ await ensureQRCode(); const qr=window.qrcode(0,'M'); qr.addData(String(val??'')); qr.make(); const modules=qr.getModuleCount(), scale=4, margin=0, size=modules*scale+margin*2*scale; const tmp=document.createElement('canvas'); tmp.width=size; tmp.height=size; const ctx=tmp.getContext('2d'); ctx.fillStyle='#FFF'; ctx.fillRect(0,0,size,size); ctx.fillStyle='#000'; for(let r=0;r<modules;r++){ for(let c=0;c<modules;c++){ if(qr.isDark(r,c)) ctx.fillRect(margin*scale+c*scale, margin*scale+r*scale, scale, scale); }} const props={ left:obj.left, top:obj.top, scaleX:obj.scaleX, scaleY:obj.scaleY, angle:obj.angle, selectable:true }; const img=new fabric.Image(tmp, props); img._kind='qr'; img._bindingKey=obj._bindingKey; img._templateValue=String(val??''); stage.value.remove(obj); stage.value.add(img); } } } await new Promise(r=>setTimeout(r,0)); }

      // Printing
      async function renderToCanvas(directionVal){ stage.value.discardActiveObject(); stage.value.requestRenderAll(); const off=document.createElement('canvas'); off.width=WIDTH_DOTS; off.height=HEIGHT_DOTS; const ctx=off.getContext('2d'); ctx.fillStyle='#FFF'; ctx.fillRect(0,0,WIDTH_DOTS,HEIGHT_DOTS); const dataURL=stage.value.toDataURL({ format:'png', multiplier:1, enableRetinaScaling:false }); await new Promise((resolve)=>{ const img=new Image(); img.onload=()=>{ ctx.drawImage(img,0,0); resolve(); }; img.src=dataURL; }); if (directionVal === 0) { const rot=document.createElement('canvas'); rot.width=WIDTH_DOTS; rot.height=HEIGHT_DOTS; const rctx=rot.getContext('2d'); rctx.translate(WIDTH_DOTS, HEIGHT_DOTS); rctx.rotate(Math.PI); rctx.drawImage(off,0,0); return rot; } return off; }

      async function onPrint(){
        try {
          status.value = 'Rendering…';
          // Direction: keep WYSIWYG -> rotate 180 and send direction 0
          const directionSend = 0; // mirrored from editor.html behavior
          // Parse data
          let arr=[]; try { const parsed=JSON.parse(dataJSON.value||'[]'); if (Array.isArray(parsed)) arr=parsed; } catch(_){}
          const records = reverse.value ? arr.slice().reverse() : arr.slice();
          const items=[];
          if (records.length>0) {
            for (let i=0;i<records.length;i++) {
              await applyBindingsFor(records[i]);
              const canvas = await renderToCanvas(0);
              const bytes = pack1bppFromCanvas(canvas, 200);
              const bytesBase64 = btoa(String.fromCharCode(...bytes));
              if (collation.value === 'uncollated') {
                for (let c=0;c<Math.max(1, Number(copies.value||1)); c++) items.push({ widthDots: WIDTH_DOTS, heightDots: HEIGHT_DOTS, bytesBase64, xDots:0, yDots:0 });
              } else {
                items.push({ widthDots: WIDTH_DOTS, heightDots: HEIGHT_DOTS, bytesBase64, xDots:0, yDots:0 });
              }
              status.value = `Prepared ${i+1}/${records.length}`;
            }
          } else {
            // Single layout
            const canvas = await renderToCanvas(0);
            const bytes = pack1bppFromCanvas(canvas, 200);
            const bytesBase64 = btoa(String.fromCharCode(...bytes));
            items.push({ widthDots: WIDTH_DOTS, heightDots: HEIGHT_DOTS, bytesBase64, xDots:0, yDots:0 });
          }
          const payload = {
            printer: selectedPrinter.value || undefined,
            items,
            sizeMm: { w: 60, h: 20 },
            gapMm: { h: 2, off: 0 },
            direction: directionSend,
            speed: 4, density: 8,
            copies: (records.length>0 && collation.value==='uncollated') ? 1 : Math.max(1, Number(copies.value||1)),
            invert: true
          };
          const res = await fetch('http://localhost:7788/print/bitmap-batch', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload) });
          const data = await res.json();
          status.value = res.ok ? ('Printed ' + (data.job||'')) : (data.error||'Error');
          // Save template
          const preview = (await renderToCanvas(0)).toDataURL('image/png');
          saveTemplate('Label ' + new Date().toLocaleString(), preview, true);
        } catch (e) { status.value = e.message || String(e); }
      }

      // Templates
      function loadTemplates(){ try { templates.value = JSON.parse(localStorage.getItem(TKEY)||'[]'); } catch(_) { templates.value = []; } }
      function saveTemplate(name, preview, silent){ const rec={ id: crypto.randomUUID?.() || String(Date.now()), name: name||('Label '+new Date().toLocaleString()), createdAt: Date.now(), preview: preview || (stage.value.toDataURL('image/png')), fabric: stageJSON() }; const arr=JSON.parse(localStorage.getItem(TKEY)||'[]'); arr.unshift(rec); localStorage.setItem(TKEY, JSON.stringify(arr.slice(0,50))); loadTemplates(); if(!silent) status.value='Template saved'; }
      function deleteTemplate(t){ const arr = JSON.parse(localStorage.getItem(TKEY)||'[]'); const next = arr.filter(x => x.id !== t.id); localStorage.setItem(TKEY, JSON.stringify(next)); loadTemplates(); }
      function loadTemplate(t){ try { stage.value.loadFromJSON(t.fabric, ()=>{ stage.value.renderAll(); status.value='Template loaded'; }); } catch(_) { status.value='Failed to load template'; } }

      function selectObject(o){ stage.value.setActiveObject(o); stage.value.requestRenderAll(); }

      async function autoLoadData(){ if ((dataJSON.value||'').trim()) return; const candidates=['data.json','../data.json','/data.json']; for (const u of candidates){ try{ const res=await fetch(u, { cache:'no-store' }); if(!res.ok) continue; const data=await res.json(); if(Array.isArray(data)){ dataJSON.value = JSON.stringify(data, null, 2); dataStatus.value = `Loaded ${data.length} records from ${u}`; break; } } catch(_){} } }

      onMounted(() => {
        // Fabric setup
        fabric.Object.prototype.transparentCorners = false;
        fabric.Object.prototype.cornerStyle = 'rect';
        fabric.Object.prototype.cornerColor = '#2563eb';
        fabric.Object.prototype.borderColor = '#2563eb';
        fabric.Object.prototype.cornerSize = 12;
        fabric.Object.prototype.rotatingPointOffset = 30;

        stage.value = new fabric.Canvas('stage', { backgroundColor:'#fff', selection:true, preserveObjectStacking:true, enableRetinaScaling:false });
        stage.value.setWidth(WIDTH_DOTS); stage.value.setHeight(HEIGHT_DOTS);
        stage.value.setZoom(1); stage.value.viewportTransform = [1,0,0,1,0,0];
        stage.value.on('object:added', snapshot);
        stage.value.on('object:removed', snapshot);
        stage.value.on('object:modified', snapshot);

        window.addEventListener('keydown', (ev)=>{ const key=ev.key; if(!['Delete','Backspace','Enter'].includes(key)) return; const ao=stage.value.getActiveObject(); if(!ao) return; if ((ao.type||'').includes('text') && ao.isEditing) return; ev.preventDefault(); deleteSelected(); });

        snapshot();
        loadTemplates();
        refreshPrinters();
        autoLoadData();
      });

      return {
        printers, selectedPrinter, stateDot, selectPrinter,
        status, copies, collation, reverse,
        dataJSON, dataStatus, previewIndex,
        stage, templates,
        fontFamily, fontSize, fontWeight, strokeWidth,
        objects, dataKeys, hasSelection, canPrint,
        refreshPrinters, addText, addBarcode, addQR, onImageFile,
        addLine, addRect, addCircle, addTriangle,
        copySelected, deleteSelected, clearCanvas, applyTextToSelection, applyStrokeToSelection,
        bindingPreview, labelOf, isBindable, selectObject,
        applyPreview, onPrint,
        loadTemplate, saveTemplate, deleteTemplate,
        undo
      };
    }
  }).mount('#app');
  </script>
</body>
</html>

