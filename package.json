{"name": "a70pro-bridge", "version": "0.1.0", "private": true, "description": "Minimal Node.js bridge to list printers and send raw ZPL/TSPL to AiYin A70Pro via CUPS lp on macOS", "main": "src/index.js", "license": "UNLICENSED", "scripts": {"start": "node src/index.js", "serve": "node src/server.js", "list": "node src/index.js --list", "test:tspl": "node src/index.js --test --lang tspl", "test:zpl": "node src/index.js --test --lang zpl", "test:text": "node src/index.js --test --lang text"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"canvas": "^3.2.0"}}