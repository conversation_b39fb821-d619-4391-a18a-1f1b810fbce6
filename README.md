AiYin A70Pro Node.js Bridge (macOS)

This minimal bridge lists CUPS printers and sends raw ZPL/TSPL test data to an AiYin A70Pro (or similar thermal label printer) using the macOS CUPS `lp` command. No external npm packages are required.

Quick start

- List printers:
  - `npm run list`
- Send a TSPL test label (default):
  - `npm run test:tspl`
- Try ZPL instead (some models prefer ZPL):
  - `npm run test:zpl`
- Target a specific printer by name:
  - `node src/index.js --test --lang tspl --printer "Your_Printer_Name"`

Label size, gap/mark, and calibration

- TSPL (TSC-style)
  - Set size and gap: `node src/index.js --test --lang tspl --size-mm 100x75 --gap-mm 2,0`
  - Black mark media: `node src/index.js --test --lang tspl --size-mm 100x75 --mark-mm 4,0 --media mark`
  - Continuous media: `node src/index.js --test --lang tspl --size-mm 100x75 --media continuous`
  - Left shift (offset): `--left-mm 2`
  - Speed/Density/Direction: `--speed 4 --density 8 --direction 1`
  - Calibrate sensors: add `--calibrate` to prepend `GAPDETECT` (auto gap/mark detect) before printing.
  - Multiple labels: `--copies 10` prints 10 labels. If your model ignores `PRINT n`, try `--use-qty` which sends `QTY 10` + `PRINT 1`.
  - DPI for layout math: `--dpi 203` (also used to scale the test layout to fit your label).
  - Label margins: `--margin-mm 2` to add/adjust content margin from the edges.

- ZPL (Zebra-style)
  - Set size in mm (converted using DPI): `node src/index.js --test --lang zpl --size-mm 100x75 --dpi 203`
  - Media type: `--media gap` (default), `--media mark`, or `--media continuous` (maps to `^MNY`, `^MNB`, `^MNN`)
  - Top/Left shifts: `--top-mm 2 --left-mm 1` (maps to `^LT` and `^LS`)
  - Calibrate sensors: add `--calibrate` to include `~JC` (media calibration) before the job.

Notes

- The script searches for a printer with a name containing "aiyin" or "a70" if `--printer` is not provided, then falls back to the default printer.
- Printing uses `lp -o raw` to send label commands directly. Ensure your CUPS queue accepts raw data or uses a Generic/Text-only-like driver. If your installed driver intercepts data, try creating a RAW queue in CUPS or switch languages (`--lang tspl|zpl|text`).
- The bundled TSPL and ZPL examples include text, barcode, and QR code for a quick sanity check. If the output looks like garbage, your printer likely expects a different language—try the other mode.

CLI

- `node src/index.js --list`
  - Lists detected printers and the default destination.
- `node src/index.js --test [--lang tspl|zpl|text] [--printer <name>]`
  - Sends a test label payload via `lp -o raw`.

Integrating with a web app

- You can wrap `printRaw()` behind a small HTTP server (e.g., `http.createServer`) to accept JSON with `printer`, `lang`, and `data` and then forward to `lp`. This repo intentionally keeps things minimal and dependency-free; if you’d like, we can extend it with an HTTP API next.

HTTP Bridge

- Start the bridge:
  - `npm run serve`
- Endpoints (CORS enabled):
  - `GET /printers` → `{ printers: string[], defaultPrinter: string|null }`
  - `POST /print/tspl` → `{ printer?, tspl: string, copies? }`
    - Sends raw TSPL text.
  - `POST /print/bitmap` → TSPL BITMAP from a pre-packed 1‑bit image:
    - Body:
      - `printer?`: queue name (optional)
      - `widthDots`/`heightDots`: image size in printer dots
      - `bytesBase64`: base64 of 1‑bpp rows, MSB→LSB, padded to whole bytes
      - `xDots?`/`yDots?`: position
      - `sizeMm?`: `{ w, h }` to set `SIZE`
      - `gapMm?`: `{ h, off? }` to set `GAP`
      - `speed?`/`density?`/`direction?`/`leftMm?`/`calibrate?`/`copies?`

Why bitmap?

- If you style in your web app (HTML/CSS/Canvas/SVG), the most reliable way to ensure the printer matches your layout is to render to a bitmap on the web side and send that to the bridge. This avoids printer font differences and TSPL limitations.

Web example: 60×20 mm at 203 dpi (480×160 dots)

```js
// Helper: pack RGBA Canvas to 1-bpp rows (MSB first)
function pack1bppFromCanvas(canvas, threshold = 180) {
  const w = canvas.width, h = canvas.height;
  const ctx = canvas.getContext('2d');
  const img = ctx.getImageData(0, 0, w, h).data;
  const bytesPerRow = Math.ceil(w / 8);
  const out = new Uint8Array(bytesPerRow * h);
  for (let y = 0; y < h; y++) {
    for (let xb = 0; xb < bytesPerRow; xb++) {
      let b = 0;
      for (let bit = 0; bit < 8; bit++) {
        const x = xb * 8 + bit;
        let v = 0;
        if (x < w) {
          const idx = (y * w + x) * 4;
          const r = img[idx], g = img[idx+1], bl = img[idx+2];
          const gray = 0.299*r + 0.587*g + 0.114*bl;
          v = gray < threshold ? 1 : 0; // 1 = black pixel
        }
        b |= v << (7 - bit);
      }
      out[y * bytesPerRow + xb] = b;
    }
  }
  return { bytes: out, bytesPerRow };
}

// Example usage in your web app
const dpi = 203;
const mmToDots = (mm) => Math.round(mm * dpi / 25.4);
const widthDots = mmToDots(60);   // 480
const heightDots = mmToDots(20);  // 160
const canvas = document.createElement('canvas');
canvas.width = widthDots;
canvas.height = heightDots;
const ctx = canvas.getContext('2d');
ctx.fillStyle = '#fff';
ctx.fillRect(0,0,canvas.width, canvas.height);
ctx.fillStyle = '#000';
ctx.font = '28px \"Noto Sans Thai\", sans-serif';
ctx.fillText('ทดสอบ ฉลาก', 10, 50);
// TODO: draw barcode/QR here if needed using a JS lib

const { bytes } = pack1bppFromCanvas(canvas, 180);
const bytesBase64 = btoa(String.fromCharCode(...bytes));

await fetch('http://localhost:7788/print/bitmap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    printer: undefined, // optional
    widthDots, heightDots, bytesBase64,
    xDots: 0, yDots: 0,
    sizeMm: { w: 60, h: 20 },
    gapMm: { h: 2, off: 0 },
    speed: 4, density: 8, direction: 1,
    copies: 1, calibrate: false
  })
});
```

Sending raw TSPL via HTTP

```bash
curl -X POST http://localhost:7788/print/tspl \
  -H 'Content-Type: application/json' \
  -d '{
    "tspl": "SIZE 60 mm, 20 mm\nGAP 2 mm, 0\nCLS\nTEXT 20,20,\"3\",0,1,1,\"HELLO\"\nPRINT 1\n"
  }'
```

Interactive editor (drag/drop/resize)

- Open `examples/editor.html` for a canvas editor powered by Fabric.js.
- Features:
  - Add/drag/resize/remove: Text, Code128 barcode, QR code, and uploaded images.
  - Adjust font family (Sarabun/Noto/System), size, and weight for Thai clarity.
  - Set copies and direction (0 front, 1 flipped) and print.
- The editor renders to a 1‑bit bitmap and posts to `/print/bitmap` with `invert: true` by default.

Troubleshooting

- No printers listed: Ensure CUPS is running (`lpstat -r`) and that your printer is added in System Settings > Printers & Scanners.
- Job submitted but nothing prints: Try another `--lang` (TSPL vs ZPL), verify queue is RAW, or try a simple `--lang text` to test connectivity.
- Wrong size/position: Adjust TSPL `SIZE`/`GAP` (in mm) or ZPL `^PW` and element coordinates for your label dimensions.
 - Calibration fails: Some printers require a physical long-press of the FEED button to calibrate. If `--calibrate` doesn’t help, try manual calibration on the device or the vendor utility.

Examples for your media (TSPL)

- 60x20 mm labels with 2 mm gap; auto-detect gap; print 1 test label:
  - `node src/index.js --test --lang tspl --size-mm 60x20 --gap-mm 2,0 --dpi 203 --margin-mm 2 --calibrate`
- Same, print 20 labels of the same layout (PRINT n):
  - `node src/index.js --test --lang tspl --size-mm 60x20 --gap-mm 2,0 --dpi 203 --margin-mm 2 --copies 20`
- If your firmware prefers QTY, use:
  - `node src/index.js --test --lang tspl --size-mm 60x20 --gap-mm 2,0 --dpi 203 --margin-mm 2 --copies 20 --use-qty`

Thai text with a TTF font (TSPL)

- Download Noto Sans Thai to the printer and print Thai text:
  - `node src/index.js --test --lang tspl --size-mm 60x20 --gap-mm 2,0 \`
  - `  --dpi 203 --margin-mm 2 \`
  - `  --ttf-path "/Library/Fonts/NotoSansThai-Regular.ttf" \`
  - `  --text "ทดสอบ ฉลาก A70Pro"`
- Options:
  - `--ttf-name` to override the filename stored on the printer (defaults to the basename of the path).
  - `--storage flash|ram` to control where the font is downloaded (default: flash). If printing once, `ram` is fine; for reuse, prefer `flash`.
- Notes:
  - The app sends `SET UNICODE ON` and `CODEPAGE UTF-8` before printing. Some firmwares ignore the codepage line when UNICODE is on — that’s okay.
  - If the printer doesn’t support TTF/Unicode via TSPL, this step will be ignored. In that case, we can add a rasterized BITMAP fallback that renders Thai text to an image and prints that.
# a70pro
Vue Editor

- File: examples/editor-vue.html
- Stack: Vue 3 (Composition API) + Tailwind (via CDN) + local Fabric + local qrcode-generator
- Features:
  - Add/drag/resize/remove: Text, Code128, QR, Images
  - Shapes: Line, Rectangle, Circle, Triangle; adjustable stroke width
  - Font family/size/weight controls
  - Copies, Direction, Left Offset (mm) controls
  - WYSIWYG direction: if Direction=1, rotates bitmap client-side and sends DIRECTION=0
  - Templates: after Print, saves layout JSON + preview to localStorage; shows history to reload/print
- Local libs required (place in examples/lib):
  - fabric.min.js (fabric@5.x dist)
  - qrcode.min.js (qrcode-generator@2.x dist)
