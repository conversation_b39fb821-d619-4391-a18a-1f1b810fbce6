// Cross-platform HTTP bridge for RAW label printing (TSPL/ZPL/ESC/POS)
// - macOS/Linux: uses CUPS `lp -o document-format=application/vnd.cups-raw`
// - Windows: uses WinSpool API via PowerShell to send RAW bytes to the printer
//
// Endpoints:
//  GET  /printers
//  GET  /printers/status
//  POST /print/tspl          -> { printer?, tspl }
//  POST /print/bitmap        -> { printer?, widthDots, heightDots, bytesBase64, xDots?, yDots?, sizeMm:{w,h}, gapMm:{h,off?}, speed?, density?, direction?, leftMm?, calibrate?, copies?, invert? }
//  POST /print/bitmap-batch  -> { printer?, items:[{ widthDots, heightDots, bytesBase64, xDots?, yDots? }], sizeMm:{w,h}, gapMm:{h,off?}, speed?, density?, direction?, leftMm?, calibrate?, copies?, invert? }

const http = require('http');
const { URL } = require('url');
const { spawn } = require('child_process');

function mmToDots(mm, dpi = 203) { return Math.round((Number(mm) || 0) * dpi / 25.4); }

function run(cmd, args = [], opts = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(cmd, args, { ...opts });
    let stdout = '';
    let stderr = '';
    if (child.stdout) child.stdout.on('data', (d) => (stdout += d.toString()));
    if (child.stderr) child.stderr.on('data', (d) => (stderr += d.toString()));
    child.on('error', reject);
    child.on('close', (code) => {
      if (code === 0) resolve({ stdout, stderr, code });
      else reject(Object.assign(new Error(`${cmd} ${args.join(' ')} failed (${code})`), { stdout, stderr, code }));
    });
  });
}

const isWin = process.platform === 'win32';

/* ----------------------- Printer enumeration ----------------------- */
async function getPrintersNix() {
  const printers = [];
  let def = null;
  try {
    const { stdout } = await run('lpstat', ['-p']);
    for (const line of stdout.split('\n')) {
      const m = line.match(/^printer\s+([^\s]+)\s+/);
      if (m) printers.push(m[1]);
    }
  } catch (_) {}
  try {
    const { stdout: d } = await run('lpstat', ['-d']);
    const md = d.match(/system default destination: (.*)/i);
    if (md) def = md[1].trim();
  } catch (_) {}
  return { printers, defaultPrinter: def };
}

async function getPrintersWin() {
  try {
    const { stdout } = await run('powershell', ['-NoProfile', '-Command', 'Get-WmiObject Win32_Printer | Select-Object Name,Default | ConvertTo-Json -Compress']);
    const data = JSON.parse(stdout || '[]');
    const arr = Array.isArray(data) ? data : (data ? [data] : []);
    const printers = arr.map(p => p.Name).filter(Boolean);
    const def = (arr.find(p => p.Default) || {}).Name || null;
    return { printers, defaultPrinter: def };
  } catch (_) {
    return { printers: [], defaultPrinter: null };
  }
}

async function getPrinters() {
  return isWin ? getPrintersWin() : getPrintersNix();
}

/* ----------------------- Printer status ----------------------- */
async function getPrintersStatusNix() {
  const result = [];
  const { printers, defaultPrinter } = await getPrinters();
  const stateMap = new Map();
  try {
    const { stdout } = await run('lpstat', ['-p']);
    for (const line of stdout.split('\n')) {
      const m = line.match(/^printer\s+([^\s]+)\s+is\s+([^\.]+)\./i);
      if (m) stateMap.set(m[1], m[2].toLowerCase());
    }
  } catch (_) {}
  const reasonMap = new Map();
  try {
    const { stdout } = await run('lpstat', ['-l', '-p']);
    let current = null;
    for (const raw of stdout.split('\n')) {
      const m = raw.match(/^printer\s+([^\s]+)\s+/i);
      if (m) { current = m[1]; reasonMap.set(current, (reasonMap.get(current) || []).concat(raw)); continue; }
      if (current) reasonMap.set(current, (reasonMap.get(current) || []).concat(raw));
    }
  } catch (_) {}
  const active = new Set();
  try {
    const { stdout } = await run('lpstat', ['-W', 'not-completed', '-o']);
    for (const line of stdout.split('\n')) {
      const m = line.match(/^([^\-\s]+)-\d+/);
      if (m) active.add(m[1]);
    }
  } catch (_) {}

  for (const name of printers) {
    const base = (stateMap.get(name) || '').trim();
    const lines = (reasonMap.get(name) || []).join('\n').toLowerCase();
    const hasOffline = /offline|not connected|unplugged|paused/.test(lines);
    let state = 'unknown';
    if (hasOffline) state = 'offline';
    else if (active.has(name)) state = 'printing';
    else if (base.includes('disabled')) state = 'disabled';
    else if (base.includes('idle')) state = 'idle';
    result.push({ name, isDefault: name === defaultPrinter, state });
  }
  return { printers: result };
}

async function getPrintersStatusWin() {
  const result = [];
  let data = [];
  try {
    const { stdout } = await run('powershell', ['-NoProfile', '-Command', 'Get-WmiObject Win32_Printer | Select-Object Name,WorkOffline,Default,PrinterStatus,PrinterState,Status | ConvertTo-Json -Compress']);
    const parsed = JSON.parse(stdout || '[]');
    data = Array.isArray(parsed) ? parsed : (parsed ? [parsed] : []);
  } catch (_) {}
  for (const p of data) {
    const name = p.Name;
    let state = 'unknown';
    const statusStr = String(p.Status || '').toLowerCase();
    const ps = Number(p.PrinterStatus || 0); // 3=Idle, 4=Printing, 7=Offline
    const offline = Boolean(p.WorkOffline) || statusStr.includes('offline') || ps === 7;
    if (offline) state = 'offline';
    else if (ps === 4) state = 'printing';
    else if (ps === 3) state = 'idle';
    result.push({ name, isDefault: !!p.Default, state });
  }
  return { printers: result };
}

async function getPrintersStatus() {
  return isWin ? getPrintersStatusWin() : getPrintersStatusNix();
}

/* ----------------------- RAW printing ----------------------- */
// macOS/Linux: stream raw bytes to CUPS
function printRawNix({ printer, data }) {
  return new Promise((resolve, reject) => {
    const args = ['-d', printer, '-o', 'document-format=application/vnd.cups-raw'];
    const child = spawn('lp', args);
    let stderr = '';
    let stdout = '';
    if (child.stderr) child.stderr.on('data', (d) => (stderr += d.toString()));
    if (child.stdout) child.stdout.on('data', (d) => (stdout += d.toString()));
    child.on('error', reject);
    child.on('close', (code) => {
      if (code === 0) resolve({ job: stdout.trim() });
      else reject(new Error(`lp ${args.join(' ')} failed (${code}): ${stderr}`));
    });
    const buf = Buffer.isBuffer(data) ? data : Buffer.from(String(data), 'ascii');
    child.stdin.end(buf);
  });
}

// Windows: use WinSpool via PowerShell + Add-Type to send RAW bytes
function printRawWin({ printer, data }) {
  return new Promise((resolve, reject) => {
    const ps = `
$PrinterName = '${printer.replace(/'/g, "''")}'
$ErrorActionPreference = 'Stop'
# read base64 from STDIN
$b64 = [Console]::In.ReadToEnd()
$bytes = [Convert]::FromBase64String($b64)

$src = @"
using System;
using System.Runtime.InteropServices;
public class RawPrinter {
  [StructLayout(LayoutKind.Sequential, CharSet=CharSet.Unicode)]
  public class DOCINFO {
    [MarshalAs(UnmanagedType.LPWStr)] public string pDocName;
    [MarshalAs(UnmanagedType.LPWStr)] public string pOutputFile;
    [MarshalAs(UnmanagedType.LPWStr)] public string pDatatype;
  }
  [DllImport("winspool.Drv", SetLastError=true, CharSet=CharSet.Unicode)]
  public static extern bool OpenPrinter(string pPrinterName, out IntPtr phPrinter, IntPtr pDefault);
  [DllImport("winspool.Drv", SetLastError=true)]
  public static extern bool ClosePrinter(IntPtr hPrinter);
  [DllImport("winspool.Drv", SetLastError=true, CharSet=CharSet.Unicode)]
  public static extern bool StartDocPrinter(IntPtr hPrinter, int Level, [In] DOCINFO di);
  [DllImport("winspool.Drv", SetLastError=true)]
  public static extern bool EndDocPrinter(IntPtr hPrinter);
  [DllImport("winspool.Drv", SetLastError=true)]
  public static extern bool StartPagePrinter(IntPtr hPrinter);
  [DllImport("winspool.Drv", SetLastError=true)]
  public static extern bool EndPagePrinter(IntPtr hPrinter);
  [DllImport("winspool.Drv", SetLastError=true)]
  public static extern bool WritePrinter(IntPtr hPrinter, byte[] pBytes, int dwCount, out int dwWritten);
}
"@
Add-Type -TypeDefinition $src -ErrorAction Stop

$h = [IntPtr]::Zero
if (-not [RawPrinter]::OpenPrinter($PrinterName, [ref]$h, [IntPtr]::Zero)) { throw "OpenPrinter failed for '$PrinterName'" }
try {
  $di = New-Object RawPrinter+DOCINFO
  $di.pDocName = "Node RAW Job"
  $di.pDatatype = "RAW"
  if (-not [RawPrinter]::StartDocPrinter($h, 1, $di)) { throw "StartDocPrinter failed" }
  try {
    if (-not [RawPrinter]::StartPagePrinter($h)) { throw "StartPagePrinter failed" }
    try {
      [int]$written = 0
      if (-not [RawPrinter]::WritePrinter($h, $bytes, $bytes.Length, [ref]$written)) { 
        $err = [Runtime.InteropServices.Marshal]::GetLastWin32Error()
        throw "WritePrinter failed (Win32=$err)"
      }
      Write-Output ("WROTE {0} BYTES" -f $written)
    } finally {
      [void][RawPrinter]::EndPagePrinter($h)
    }
  } finally {
    [void][RawPrinter]::EndDocPrinter($h)
  }
} finally {
  [void][RawPrinter]::ClosePrinter($h)
}
`;
    const child = spawn('powershell', ['-NoProfile', '-ExecutionPolicy', 'Bypass', '-Command', ps], { stdio: ['pipe', 'pipe', 'pipe'] });
    let stdout = '', stderr = '';
    child.stdout.on('data', d => stdout += d.toString());
    child.stderr.on('data', d => stderr += d.toString());
    child.on('error', reject);
    child.on('close', (code) => {
      if (code === 0) resolve({ job: stdout.trim() || 'OK' });
      else reject(new Error(`Win RAW print failed (${code}): ${stderr || stdout}`));
    });
    // send base64 over stdin to avoid encoding issues
    const buf = Buffer.isBuffer(data) ? data : Buffer.from(String(data), 'ascii');
    const b64 = buf.toString('base64');
    child.stdin.end(b64);
  });
}

function printRaw({ printer, data }) {
  return isWin ? printRawWin({ printer, data }) : printRawNix({ printer, data });
}

/* ----------------------- HTTP helpers ----------------------- */
function ok(res, obj) {
  const s = JSON.stringify(obj || { ok: true });
  res.writeHead(200, { 'content-type': 'application/json' });
  res.end(s);
}
function bad(res, status, msg) {
  res.writeHead(status || 400, { 'content-type': 'application/json' });
  res.end(JSON.stringify({ error: msg }));
}
function allowCORS(req, res) {
  res.setHeader('Access-Control-Allow-Origin', '*'); // consider restricting to your domain
  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
}
function readBody(req) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    let size = 0;
    const limit = 5 * 1024 * 1024; // 5MB
    req.on('data', (c) => {
      size += c.length;
      if (size > limit) { reject(new Error('Body too large')); req.destroy(); return; }
      chunks.push(c);
    });
    req.on('end', () => {
      try { resolve(JSON.parse(Buffer.concat(chunks).toString('utf8') || '{}')); }
      catch (e) { reject(e); }
    });
    req.on('error', reject);
  });
}

/* ----------------------- TSPL composers ----------------------- */
function composeTSPLBitmapJob(payload) {
  const {
    widthDots, heightDots, bytesBase64,
    xDots = 0, yDots = 0,
    sizeMm, gapMm,
    speed = 4, density = 8, direction = 1, leftMm,
    calibrate = false, copies = 1, invert = true
  } = payload;

  if (!widthDots || !heightDots || !bytesBase64) throw new Error('widthDots, heightDots, bytesBase64 are required');
  let bytes = Buffer.from(bytesBase64, 'base64');
  const bytesPerRow = Math.ceil(widthDots / 8);
  if (bytes.length !== bytesPerRow * heightDots) throw new Error(`bytes length mismatch: expected ${bytesPerRow * heightDots}, got ${bytes.length}`);
  if (invert) {
    const tmp = Buffer.allocUnsafe(bytes.length);
    for (let i = 0; i < bytes.length; i++) tmp[i] = (~bytes[i]) & 0xFF;
    bytes = tmp;
  }

  const header = [];
  if (sizeMm && sizeMm.w && sizeMm.h) header.push(`SIZE ${sizeMm.w} mm, ${sizeMm.h} mm`);
  if (gapMm && gapMm.h != null) header.push(`GAP ${gapMm.h} mm, ${gapMm.off || 0}`);
  header.push(`SPEED ${speed}`);
  header.push(`DENSITY ${density}`);
  header.push(`DIRECTION ${direction}`);
  if (leftMm) header.push(`OFFSET ${leftMm} mm`);
  header.push('REFERENCE 0,0');
  if (calibrate) header.push('GAPDETECT');
  const n = Math.max(1, Number(copies || 1));

  const out = [];
  const pushAscii = (s) => out.push(Buffer.from(s.replace(/\n/g, '\r\n') + '\r\n', 'ascii'));
  const pushBin = (b) => out.push(Buffer.isBuffer(b) ? b : Buffer.from(b));
  header.forEach(pushAscii);

  for (let i = 0; i < n; i++) {
    pushAscii('CLS');
    pushAscii(`BITMAP ${xDots},${yDots},${bytesPerRow},${heightDots},1,`);
    pushBin(bytes);
    pushAscii('');
    pushAscii('PRINT 1');
  }
  return Buffer.concat(out);
}

function composeTSPLBitmapBatchJob(payload) {
  const {
    items,
    sizeMm, gapMm,
    speed = 4, density = 8, direction = 1, leftMm,
    calibrate = false, copies = 1, invert = true
  } = payload || {};
  if (!Array.isArray(items) || items.length === 0) throw new Error('items[] required');

  const header = [];
  if (sizeMm && sizeMm.w && sizeMm.h) header.push(`SIZE ${sizeMm.w} mm, ${sizeMm.h} mm`);
  if (gapMm && gapMm.h != null) header.push(`GAP ${gapMm.h} mm, ${gapMm.off || 0}`);
  header.push(`SPEED ${speed}`);
  header.push(`DENSITY ${density}`);
  header.push(`DIRECTION ${direction}`);
  if (leftMm) header.push(`OFFSET ${leftMm} mm`);
  header.push('REFERENCE 0,0');
  if (calibrate) header.push('GAPDETECT');

  const out = [];
  const pushAscii = (s) => out.push(Buffer.from(s.replace(/\n/g, '\r\n') + '\r\n', 'ascii'));
  const pushBin = (b) => out.push(Buffer.isBuffer(b) ? b : Buffer.from(b));
  header.forEach(pushAscii);

  const n = Math.max(1, Number(copies || 1));
  for (let ci = 0; ci < n; ci++) {
    for (const it of items) {
      const { widthDots, heightDots, bytesBase64 } = it || {};
      const xDots = Number.isFinite(it.xDots) ? it.xDots : 0;
      const yDots = Number.isFinite(it.yDots) ? it.yDots : 0;
      if (!widthDots || !heightDots || !bytesBase64) throw new Error('item missing widthDots/heightDots/bytesBase64');
      const bytesPerRow = Math.ceil(widthDots / 8);
      let bytes = Buffer.from(bytesBase64, 'base64');
      if (bytes.length !== bytesPerRow * heightDots) throw new Error('item bytes length mismatch');
      if (invert) {
        const tmp = Buffer.allocUnsafe(bytes.length);
        for (let i = 0; i < bytes.length; i++) tmp[i] = (~bytes[i]) & 0xFF;
        bytes = tmp;
      }
      pushAscii('CLS');
      pushAscii(`BITMAP ${xDots},${yDots},${bytesPerRow},${heightDots},1,`);
      pushBin(bytes);
      pushAscii('');
      pushAscii('PRINT 1');
    }
  }
  return Buffer.concat(out);
}

/* ----------------------- HTTP server ----------------------- */
const server = http.createServer(async (req, res) => {
  allowCORS(req, res);
  if (req.method === 'OPTIONS') { res.writeHead(204); res.end(); return; }

  const url = new URL(req.url, 'http://localhost');
  try {
    if (req.method === 'GET' && url.pathname === '/printers') {
      const info = await getPrinters();
      ok(res, info);
      return;
    }
    if (req.method === 'GET' && url.pathname === '/printers/status') {
      const info = await getPrintersStatus();
      ok(res, info);
      return;
    }

    if (req.method === 'POST' && url.pathname === '/print/tspl') {
      const body = await readBody(req);
      const { printers, defaultPrinter } = await getPrinters();
      const target = body.printer || printers.find((p) => /aiyin|a70/i.test(p)) || defaultPrinter || printers[0];
      if (!target) return bad(res, 404, 'No printer found');
      if (!body.tspl) return bad(res, 400, 'Missing tspl');
      const info = await printRaw({ printer: target, data: body.tspl });
      ok(res, { ok: true, printer: target, job: info.job });
      return;
    }

    if (req.method === 'POST' && url.pathname === '/print/bitmap') {
      const body = await readBody(req);
      const buf = composeTSPLBitmapJob(body);
      const { printers, defaultPrinter } = await getPrinters();
      const target = body.printer || printers.find((p) => /aiyin|a70/i.test(p)) || defaultPrinter || printers[0];
      if (!target) return bad(res, 404, 'No printer found');
      const info = await printRaw({ printer: target, data: buf });
      ok(res, { ok: true, printer: target, job: info.job });
      return;
    }

    if (req.method === 'POST' && url.pathname === '/print/bitmap-batch') {
      const body = await readBody(req);
      const buf = composeTSPLBitmapBatchJob(body);
      const { printers, defaultPrinter } = await getPrinters();
      console.log('Print request:', {
        bodyPrinter: body.printer,
        availablePrinters: printers,
        defaultPrinter
      });
      const target = body.printer || printers.find((p) => /aiyin|a70/i.test(p)) || defaultPrinter || printers[0];
      if (!target) return bad(res, 404, `No printer found. Available: ${printers.join(', ')}`);
      console.log('Selected printer:', target);
      const info = await printRaw({ printer: target, data: buf });
      ok(res, { ok: true, printer: target, job: info.job });
      return;
    }

    bad(res, 404, 'Not Found');
  } catch (e) {
    bad(res, 500, e.message || String(e));
  }
});

const PORT = process.env.PORT || 7788;
server.listen(PORT, () => {
  console.log(`Bridge listening on http://localhost:${PORT} (${isWin ? 'Windows' : 'POSIX'})`);
});
