// src/index.js
// Node raw print helper for TSPL/ZPL with Thai bitmap support and auto-fit layout.

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/* ------------------- child process utils ------------------- */
function run(cmd, args = [], opts = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(cmd, args, { ...opts });
    let stdout = '';
    let stderr = '';
    if (child.stdout) child.stdout.on('data', (d) => (stdout += d.toString()));
    if (child.stderr) child.stderr.on('data', (d) => (stderr += d.toString()));
    child.on('error', reject);
    child.on('close', (code) => {
      if (code === 0) resolve({ stdout, stderr, code });
      else reject(Object.assign(new Error(`${cmd} ${args.join(' ')} failed (${code})`), { stdout, stderr, code }));
    });
  });
}

async function getPrinters() {
  const printers = [];
  let def = null;
  try {
    const { stdout } = await run('lpstat', ['-p']);
    for (const line of stdout.split('\n')) {
      const m = line.match(/^printer\s+([^\s]+)\s+/);
      if (m) printers.push(m[1]);
    }
  } catch (e) {
    // ignore; CUPS may be missing
  }
  try {
    const { stdout: d } = await run('lpstat', ['-d']);
    const md = d.match(/system default destination: (.*)/i);
    if (md) def = md[1].trim();
  } catch (_) {}
  return { printers, defaultPrinter: def };
}

function printRaw({ printer, data }) {
  return new Promise((resolve, reject) => {
    const args = ['-d', printer, '-o', 'raw'];
    const child = spawn('lp', args);
    let stderr = '';
    if (child.stderr) child.stderr.on('data', (d) => (stderr += d.toString()));
    child.on('error', reject);
    child.on('close', (code) => {
      if (code === 0) resolve();
      else reject(new Error(`lp ${args.join(' ')} failed (${code}): ${stderr}`));
    });
    // If data is a Buffer, end without encoding; else default to utf8.
    if (Buffer.isBuffer(data)) child.stdin.end(data);
    else child.stdin.end(data, 'utf8');
  });
}

/* ------------------- math / image helpers ------------------- */
function mmToDots(mm, dpi) {
  return Math.round((Number(mm) || 0) * dpi / 25.4);
}

function rgbaToMonoBytes(imgData, width, height, threshold = 180) {
  const bytesPerRow = Math.ceil(width / 8);
  const out = Buffer.alloc(bytesPerRow * height);
  for (let y = 0; y < height; y++) {
    for (let xb = 0; xb < bytesPerRow; xb++) {
      let b = 0;
      for (let bit = 0; bit < 8; bit++) {
        const x = xb * 8 + bit;
        let v = 0;
        if (x < width) {
          const idx = (y * width + x) * 4;
          const r = imgData[idx], g = imgData[idx + 1], b2 = imgData[idx + 2];
          const gray = 0.299 * r + 0.587 * g + 0.114 * b2;
          v = gray < threshold ? 1 : 0; // 1 = black
        }
        b |= v << (7 - bit);
      }
      out[y * bytesPerRow + xb] = b;
    }
  }
  return { bytes: out, bytesPerRow };
}

function toHex(buf) {
  return buf.toString('hex').toUpperCase();
}

/* ------------------- Thai bitmap renderers (lazy canvas) ------------------- */
async function renderThaiMono({ text, widthDots, heightDots, fontPath, threshold = 180 }) {
  let createCanvas, registerFont;
  try {
    ({ createCanvas, registerFont } = require('canvas'));
  } catch (_) {
    throw new Error('Thai text requires `canvas`. Install: npm i canvas (and system libs: cairo/pango).');
  }

  if (fontPath) {
    try { registerFont(fontPath, { family: 'ThaiFont' }); } catch (_) {}
  }

  const canvas = createCanvas(widthDots, heightDots);
  const ctx = canvas.getContext('2d');
  ctx.fillStyle = '#FFFFFF';
  ctx.fillRect(0, 0, widthDots, heightDots);
  ctx.fillStyle = '#000000';

  // Auto font size ~ 80% of height
  const size = Math.max(10, Math.floor(heightDots * 0.8));
  ctx.font = `${size}px "ThaiFont", "Noto Sans Thai", sans-serif`;

  // Center vertically, left pad 2px
  const y = Math.round(heightDots * 0.85);
  ctx.fillText(String(text || ''), 2, y);

  const img = ctx.getImageData(0, 0, widthDots, heightDots);
  return rgbaToMonoBytes(img.data, widthDots, heightDots, threshold);
}

/* ------------------- TSPL & ZPL builders with auto-fit ------------------- */
async function buildTSPL(opts = {}) {
  const now = new Date().toISOString();
  const p = { ...opts };
  const dpi = Number(p.dpi || 203);

  const media = (p.media || 'gap').toLowerCase();
  const widthMm = p.widthMm || 100;
  const heightMm = p.lengthMm || 100;
  const marginMm = p.marginMm != null ? Number(p.marginMm) : 2;

  const margin = mmToDots(marginMm, dpi);
  const labelW = mmToDots(widthMm, dpi);
  const labelH = mmToDots(heightMm, dpi) - 2 * margin;

  // Layout: title, barcode, optional QR, optional timestamp, optional Thai box
  const wantTitle = p.noTitle ? false : true;
  const titleH = wantTitle ? Math.min(mmToDots(6, dpi), Math.max(mmToDots(4, dpi), Math.floor(labelH * 0.10))) : 0;

  let bcH = p.barcodeMm ? mmToDots(p.barcodeMm, dpi) : Math.floor(labelH * 0.40);
  const bcMin = mmToDots(6, dpi);
  const bcMax = Math.max(bcMin, labelH - titleH - mmToDots(6, dpi));
  bcH = Math.max(bcMin, Math.min(bcH, bcMax));

  let qrH = labelH >= mmToDots(30, dpi) ? Math.min(mmToDots(16, dpi), Math.floor(labelH * 0.25)) : 0;
  let tsH = qrH ? mmToDots(4, dpi) : (labelH >= mmToDots(24, dpi) ? mmToDots(4, dpi) : 0);

  // Thai box height if requested
  const thaiH = p.thai ? Math.min(mmToDots(12, dpi), Math.max(mmToDots(8, dpi), Math.floor(labelH * 0.20))) : 0;

  let used = titleH + bcH + (qrH || 0) + (tsH || 0) + (thaiH || 0) + mmToDots(6, dpi);
  if (used > labelH) { tsH = 0; used = titleH + bcH + (qrH || 0) + (thaiH || 0) + mmToDots(6, dpi); }
  if (used > labelH) { qrH = 0; used = titleH + bcH + (thaiH || 0) + mmToDots(6, dpi); }
  if (used > labelH) { // last resort: shrink barcode a bit
    const extra = used - labelH;
    bcH = Math.max(bcMin, bcH - extra);
  }

  const copies = Math.max(1, Number(p.copies || 1));
  const refXmm = (p.refMm && typeof p.refMm.x === 'number') ? p.refMm.x : 0;
  const refYmm = (p.refMm && typeof p.refMm.y === 'number') ? p.refMm.y : 0;
  const headerLines = [
    `SIZE ${widthMm} mm, ${heightMm} mm`,
    media === 'mark'
      ? `BLINE ${p.markMm || 2} mm, ${p.markOffsetMm || 0}`
      : media === 'continuous'
        ? 'GAP 0 mm, 0'
        : `GAP ${p.gapMm || 2} mm, ${p.gapOffsetMm || 0}`,
    `SPEED ${p.speed ?? 4}`,
    `DENSITY ${p.density ?? 8}`,
    `DIRECTION ${p.direction ?? 0}`,
    p.leftMm ? `OFFSET ${p.leftMm} mm` : null,
    `REFERENCE ${mmToDots(refXmm, dpi)},${mmToDots(refYmm, dpi)}`,
    p.calibrate ? 'GAPDETECT' : null,
    p.useQty ? `QTY ${copies}` : null,
    'CLS'
  ].filter(Boolean);

  // Build as Buffer list to interleave BITMAP/TTF binary correctly
  const out = [];
  const pushAscii = (s) => out.push(Buffer.from(s.replace(/\n/g, '\r\n') + '\r\n', 'ascii'));
  const pushBin = (b) => out.push(Buffer.isBuffer(b) ? b : Buffer.from(b));

  headerLines.forEach(pushAscii);

  // Optional: download a TTF and enable Unicode text (no canvas needed)
  if (p.ttfPath || p.ttfName || p.text) {
    const ttfPath = p.ttfPath;
    let fontBuf = null;
    if (ttfPath) {
      try { fontBuf = fs.readFileSync(ttfPath); } catch (e) { throw new Error(`Failed to read TTF: ${ttfPath}. ${e.message}`); }
    }
    const fontName = p.ttfName || (ttfPath ? path.basename(ttfPath) : 'font.ttf');
    const storage = String(p.storage || 'flash').toLowerCase();
    const store = storage.startsWith('f') ? 'F' : 'B'; // F: flash, B: RAM/DRAM
    if (fontBuf) {
      pushAscii(`DOWNLOAD ${store},"${fontName}",${fontBuf.length},`);
      pushBin(fontBuf);
      pushAscii('');
    }
    pushAscii('SET UNICODE ON');
    pushAscii('CODEPAGE UTF-8');
    if (p.text || p.thai) {
      const text = String(p.text || p.thai);
      const x = margin;
      const yTtf = margin;
      pushAscii(`TEXT ${x},${yTtf},"${fontName}",0,1,1,"${text}"`);
    }
  }

  let y = (typeof p.yMm === 'number') ? mmToDots(p.yMm, dpi) : margin;

  if (wantTitle) {
    // Built-in TEXT is ASCII-safe (no Thai). Using font 3 as sample.
    // TEXT x,y,"font",rotation,xmul,ymul,"text"
    pushAscii(`TEXT ${margin},${y},"3",0,1,1,"AiYin A70Pro Test"`);
    y += titleH + mmToDots(2, dpi);
  }

  // Barcode 128
  if (!p.noBarcode) {
    pushAscii(`BARCODE ${margin},${y},"128",${bcH},1,0,2,2,"A70PRO"`);
    y += bcH + mmToDots(2, dpi);
  }

  if (qrH) {
    // TSPL QRCODE: QRCODE x,y, error, module, rotation, model, "data"
    // Use module size 4 as default
    pushAscii(`QRCODE ${margin},${y},L,4,A,0,"Hello from Node.js"`);
    y += qrH + mmToDots(2, dpi);
  }

  if (tsH) {
    pushAscii(`TEXT ${margin},${y},"3",0,1,1,"${now}"`);
    y += tsH + mmToDots(1, dpi);
  }

  // Thai text as BITMAP (if provided)
  if (p.thai && !p.ttfPath && !p.ttfName) {
    // Make a text box; leave 2mm side margins
    const boxW = Math.max(10, labelW - 2 * margin);
    const boxH = thaiH || Math.min(mmToDots(12, dpi), Math.floor(labelH * 0.25));
    let { bytes, bytesPerRow } = await renderThaiMono({
      text: p.thai,
      widthDots: boxW,
      heightDots: boxH,
      fontPath: p.fontPath,
      threshold: p.threshold || 180
    });
    if (p.invertBitmap !== false) {
      const inv = Buffer.allocUnsafe(bytes.length);
      for (let i = 0; i < bytes.length; i++) inv[i] = (~bytes[i]) & 0xFF;
      bytes = inv;
    }
    // Place at current y
    pushAscii(`BITMAP ${margin},${y},${bytesPerRow},${boxH},1,`);
    pushBin(bytes); // TSPL expects binary immediately after the header
    y += boxH + mmToDots(1, dpi);
  }

  // Print
  pushAscii(p.useQty ? 'PRINT 1' : `PRINT ${copies}`);

  return Buffer.concat(out);
}

async function buildZPL(opts = {}) {
  const now = new Date().toISOString();
  const p = { ...opts };
  const dpi = Number(p.dpi || 203);

  const widthDots = p.widthDots ?? (p.widthMm ? mmToDots(p.widthMm, dpi) : 812);
  const lengthDots = p.lengthDots ?? (p.lengthMm ? mmToDots(p.lengthMm, dpi) : 812);
  const topShiftDots = p.topMm ? mmToDots(p.topMm, dpi) : 0;
  const leftShiftDots = p.leftMm ? mmToDots(p.leftMm, dpi) : 0;

  const marginMm = p.marginMm != null ? Number(p.marginMm) : 2;
  const margin = mmToDots(marginMm, dpi);
  const labelH = lengthDots - 2 * margin;

  const wantTitle = p.noTitle ? false : true;
  const titleH = wantTitle ? Math.min(mmToDots(6, dpi), Math.max(mmToDots(4, dpi), Math.floor(labelH * 0.10))) : 0;

  let bcH = Math.floor(labelH * 0.40);
  const bcMin = mmToDots(8, dpi);
  const bcMax = Math.max(bcMin, labelH - titleH - mmToDots(6, dpi));
  bcH = Math.max(bcMin, Math.min(bcH, bcMax));

  let qrH = labelH >= mmToDots(30, dpi) ? Math.min(mmToDots(16, dpi), Math.floor(labelH * 0.25)) : 0;
  let tsH = qrH ? mmToDots(4, dpi) : (labelH >= mmToDots(24, dpi) ? mmToDots(4, dpi) : 0);

  const thaiH = p.thai ? Math.min(mmToDots(12, dpi), Math.max(mmToDots(8, dpi), Math.floor(labelH * 0.20))) : 0;

  let used = titleH + bcH + (qrH || 0) + (tsH || 0) + (thaiH || 0) + mmToDots(6, dpi);
  if (used > labelH) { tsH = 0; used = titleH + bcH + (qrH || 0) + (thaiH || 0) + mmToDots(6, dpi); }
  if (used > labelH) { qrH = 0; used = titleH + bcH + (thaiH || 0) + mmToDots(6, dpi); }
  if (used > labelH) { const extra = used - labelH; bcH = Math.max(bcMin, bcH - extra); }

  const lines = [];
  lines.push('^XA');
  // Media type
  const media = (p.media || 'gap').toLowerCase();
  const mn = media === 'mark' ? 'B' : media === 'continuous' ? 'N' : 'Y';
  lines.push(`^MN${mn}`);
  lines.push(`^PW${widthDots}`);
  lines.push(`^LL${lengthDots}`);
  lines.push(`^LT${topShiftDots}`);
  lines.push(`^LS${leftShiftDots}`);
  lines.push('^LH0,0');

  if (p.calibrate) lines.push('~JC');

  // Content
  let y = margin;
  if (wantTitle) {
    const s = Math.floor(titleH * 0.8);
    lines.push(`^FO${margin},${y}^A0N,${s},${s}^FDAiYin A70Pro Test^FS`);
    y += titleH + mmToDots(2, dpi);
  }

  // Barcode 128
  // ^BYw,r,h  then ^BCo,h,f,g,m
  lines.push(`^FO${margin},${y}^BY2,2,${bcH}^BCN,${bcH},Y,N,N^FDA70PRO^FS`);
  y += bcH + mmToDots(2, dpi);

  if (qrH) {
    lines.push(`^FO${margin},${y}^BQN,2,6^FDQA,Hello from Node.js^FS`);
    y += qrH + mmToDots(2, dpi);
  }

  if (tsH) {
    const s = Math.floor(tsH * 0.9);
    lines.push(`^FO${margin},${y}^A0N,${s},${s}^FD${now}^FS`);
    y += tsH + mmToDots(1, dpi);
  }

  // Thai via ^GFA (bitmap)
  if (p.thai) {
    const boxW = Math.max(10, widthDots - 2 * margin);
    const boxH = thaiH || Math.min(mmToDots(12, dpi), Math.floor(labelH * 0.25));
    const { bytes, bytesPerRow } = await renderThaiMono({
      text: p.thai,
      widthDots: boxW,
      heightDots: boxH,
      fontPath: p.fontPath,
      threshold: p.threshold || 180
    });
    const totalBytes = bytes.length;
    const hex = toHex(bytes);
    // ^GFA,t,r,b,data  (we set total = used = bytes.length)
    lines.push(`^FO${margin},${y}^GFA,${totalBytes},${totalBytes},${bytesPerRow},${hex}^FS`);
    y += boxH + mmToDots(1, dpi);
  }

  lines.push('^XZ');
  lines.push(''); // final newline

  // ZPL is pure ASCII; return string
  return lines.join('\n');
}

/* ------------------- Unified builder ------------------- */
async function buildTestData(lang = 'tspl', opts = {}) {
  const mode = (lang || '').toLowerCase();
  if (mode === 'zpl') return buildZPL(opts);
  if (mode === 'text') {
    const now = new Date().toISOString();
    return `Node.js Test Print\nAiYin A70Pro\n${now}\n\n`;
  }
  // default TSPL
  return buildTSPL(opts);
}

/* ------------------- CLI ------------------- */
function parseArgs(argv) {
  const out = { _: [] };
  for (let i = 2; i < argv.length; i++) {
    const a = argv[i];
    if (a === '--list') out.list = true;
    else if (a === '--print' || a === '--test') out.test = true;
    else if (a === '--lang') out.lang = argv[++i];
    else if (a === '--printer' || a === '-p') out.printer = argv[++i];
    else if (a === '--size-mm') {
      const v = (argv[++i] || '').toLowerCase().replace(/,/g, 'x');
      const [w, h] = v.split('x').map(Number);
      out.widthMm = w; out.lengthMm = h;
    }
    else if (a === '--gap-mm') {
      const v = (argv[++i] || '').replace(/x/g, ',');
      const [g, o] = v.split(',').map(Number);
      out.gapMm = g; out.gapOffsetMm = o || 0;
    }
    else if (a === '--mark-mm') {
      const v = (argv[++i] || '').replace(/x/g, ',');
      const [m, o] = v.split(',').map(Number);
      out.markMm = m; out.markOffsetMm = o || 0;
      out.media = 'mark';
    }
    else if (a === '--media') out.media = argv[++i];
    else if (a === '--dpi') out.dpi = Number(argv[++i]);
    else if (a === '--margin-mm') out.marginMm = Number(argv[++i]);
    else if (a === '--top-mm') out.topMm = Number(argv[++i]);
    else if (a === '--left-mm') out.leftMm = Number(argv[++i]);
    else if (a === '--speed') out.speed = Number(argv[++i]);
    else if (a === '--density') out.density = Number(argv[++i]);
    else if (a === '--direction') out.direction = Number(argv[++i]);
    else if (a === '--calibrate') out.calibrate = true;
    else if (a === '--copies' || a === '--qty' || a === '-n') out.copies = Math.max(1, Number(argv[++i]));
    else if (a === '--use-qty') out.useQty = true;
    else if (a === '--no-title') out.noTitle = true;
    else if (a === '--barcode-mm') out.barcodeMm = Number(argv[++i]);
    else if (a === '--y-mm') out.yMm = Number(argv[++i]);
    else if (a === '--ref-mm') {
      const v = (argv[++i] || '').replace(/x/ig, ',');
      const [rx, ry] = v.split(',').map(Number);
      out.refMm = { x: rx || 0, y: ry || 0 };
    }
    else if (a === '--thai') out.thai = argv[++i];
    else if (a === '--font') out.fontPath = argv[++i];
    else if (a === '--no-barcode') out.noBarcode = true;
    else if (a === '--ttf-path') out.ttfPath = argv[++i];
    else if (a === '--ttf-name') out.ttfName = argv[++i];
    else if (a === '--text') out.text = argv[++i];
    else if (a === '--storage') out.storage = argv[++i]; // flash|ram
    else if (a === '--threshold') out.threshold = Number(argv[++i]);
    else if (a === '--invert-bitmap') out.invertBitmap = true;
    else if (a === '--no-invert-bitmap') out.invertBitmap = false;
    else if (a === '--dry-run') out.dry = true;
    else if (a === '--out') out.out = argv[++i];
    else out._.push(a);
  }
  return out;
}

/* ------------------- main ------------------- */
async function main() {
  const args = parseArgs(process.argv);
  const { printers, defaultPrinter } = await getPrinters().catch((e) => {
    console.error('Failed to query printers via CUPS (lpstat). Ensure CUPS is available on this OS.', e.message);
    return { printers: [], defaultPrinter: null };
  });

  if (args.list) {
    console.log('Detected printers:');
    for (const p of printers) console.log(`- ${p}${p === defaultPrinter ? ' (default)' : ''}`);
    if (!printers.length) console.log('(none found)');
    return;
  }

  if (args.test) {
    let target = args.printer;
    if (!target) {
      target = printers.find((p) => /aiyin|a70/i.test(p)) || defaultPrinter || printers[0];
    }
    if (!target && !args.dry) {
      console.error('No printer found. Run with --list to see available printers, or pass --printer <name>.');
      process.exit(2);
    }

    const lang = (args.lang || 'tspl').toLowerCase();
    const payload = await buildTestData(lang, args);

    if (args.dry || args.out) {
      const outPath = path.resolve(args.out || `out_${lang}.prn`);
      fs.writeFileSync(outPath, Buffer.isBuffer(payload) ? payload : Buffer.from(payload, 'ascii'));
      console.log(`Saved raw job to: ${outPath}`);
      return;
    }

    console.log(`Sending ${lang.toUpperCase()} job to: ${target}`);
    await printRaw({ printer: target, data: payload });
    console.log('Print job submitted.');
    return;
  }

  console.log('Usage:');
  console.log('  node src/index.js --list');
  console.log('  node src/index.js --test [--lang tspl|zpl|text] [--printer <name>] [--dry-run] [--out file.prn]');
  console.log('Sizing/media:');
  console.log('  --size-mm <W>x<H>         e.g., 40x30');
  console.log('  --gap-mm <H>[,<off>]      gap media (TSPL)');
  console.log('  --mark-mm <H>[,<off>]     mark media (TSPL) and sets --media mark');
  console.log('  --media <gap|mark|continuous>');
  console.log('  --dpi <203|300|...>       default 203');
  console.log('  --top-mm <mm>             ZPL ^LT');
  console.log('  --left-mm <mm>            ZPL ^LS / TSPL OFFSET');
  console.log('  --margin-mm <mm>          default 2');
  console.log('  --ref-mm <x>,<y>          shift the coordinate origin in mm (TSPL REFERENCE)');
  console.log('  --y-mm <mm>               start Y for layout (overrides top margin)');
  console.log('  --speed <1-6>             TSPL');
  console.log('  --density <0-15>          TSPL');
  console.log('  --direction <0|1>         TSPL');
  console.log('  --calibrate               media calibration');
  console.log('Copies:');
  console.log('  --copies <n>              TSPL: PRINT n (default) | use --use-qty for QTY n + PRINT 1');
  console.log('Thai bitmap:');
  console.log('  --thai \"ข้อความ\"          render Thai as bitmap (requires canvas)');
  console.log('  --font \"/path/Thai.ttf\"   Thai font (e.g., NotoSansThai-Regular.ttf)');
  console.log('  --threshold <0..255>      BW threshold (default 180)');
  console.log('  --invert-bitmap           invert 1-bit pixels (on by default)');
  console.log('  --no-invert-bitmap        keep original 1-bit polarity');
  console.log('Thai via TTF (no canvas):');
  console.log('  --ttf-path "/Library/Fonts/NotoSansThai-Regular.ttf"');
  console.log('  --ttf-name NotoSansThai-Regular.ttf  (optional, printer-side filename)');
  console.log('  --text "ทดสอบ"             UTF-8 text to print');
  console.log('  --storage flash|ram       where to DOWNLOAD the font (default flash)');
  console.log('Other:');
  console.log('  --no-barcode              hide the sample barcode');
  console.log('Layout tuning:');
  console.log('  --no-title                remove the sample title line');
  console.log('  --barcode-mm <mm>         set barcode height in mm');
  console.log('Dry run / file output:');
  console.log('  --dry-run                 don’t print, just write .prn');
  console.log('  --out file.prn            path to save raw data');
}

main().catch((e) => {
  console.error(e.stack || e.message || String(e));
  process.exit(1);
});
